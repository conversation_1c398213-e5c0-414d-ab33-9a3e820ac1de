import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { RootState } from '@/store/authStore';
import { setCredentials, clearCredentials } from '@/slices/authSlice';

// Base query
const rawBaseQuery = fetchBaseQuery({
  baseUrl: process.env.NEXT_PUBLIC_API_ENDPOINT,
  prepareHeaders: (headers, { getState }) => {
    const { token } = (getState() as RootState).auth;
    if (token) {
      headers.set('authorization', `Bearer ${token}`);
    }
    return headers;
  },
  credentials: 'include',
});

// ✅ Wrapper with refresh retry
const baseQueryWithReauth: typeof rawBaseQuery = async (
  args,
  api,
  extraOptions
) => {
  let result = await rawBaseQuery(args, api, extraOptions);

  if (result.error && (result.error as any).status === 401) {
    const state = api.getState() as RootState;
    const { refreshToken } = state.auth;

    if (!refreshToken) {
      api.dispatch(clearCredentials());
      return result;
    }

    // try refresh
    const refreshResult = await rawBaseQuery(
      {
        url: '/api/v1/auth/refresh',
        method: 'POST',
        body: { refreshToken },
      },
      api,
      extraOptions
    );

    if (refreshResult.data) {
      const data = refreshResult.data as any;
      api.dispatch(
        setCredentials({
          token: data.accessToken,
          refreshToken: data.refreshToken ?? refreshToken,
          user: state.auth.user!,
        })
      );
      // retry original request
      result = await rawBaseQuery(args, api, extraOptions);
    } else {
      api.dispatch(clearCredentials());
    }
  }

  return result;
};

// ✅ API Endpoints
export const authApi = createApi({
  reducerPath: 'authApi',
  baseQuery: baseQueryWithReauth,
  endpoints: builder => ({
    login: builder.mutation<any, { email: string; password: string }>({
      query: credentials => ({
        url: '/api/v1/auth/signin',
        method: 'POST',
        body: credentials,
      }),
    }),
    signup: builder.mutation<any, any>({
      query: body => ({
        url: '/api/v1/auth/signup',
        method: 'POST',
        body,
      }),
    }),
    otpVerify: builder.mutation<any, any>({
      query: body => ({
        url: '/api/v1/verify-email',
        method: 'POST',
        body,
      }),
    }),
    guestLogin: builder.mutation<any, void>({
      query: () => ({
        url: '/api/v1/auth/guest-login',
        method: 'POST',
      }),
    }),
    logout: builder.mutation<{ success: boolean }, void>({
      query: () => ({
        url: '/api/v1/auth/signout',
        method: 'POST',
      }),
    }),
    refreshToken: builder.mutation<any, { refreshToken: string }>({
      query: body => ({
        url: '/api/v1/auth/refresh',
        method: 'POST',
        body,
      }),
    }),
  }),
});

export const {
  useLoginMutation,
  useSignupMutation,
  useOtpVerifyMutation,
  useGuestLoginMutation,
  useLogoutMutation,
  useRefreshTokenMutation,
} = authApi;
