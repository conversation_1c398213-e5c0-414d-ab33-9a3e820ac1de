import { apiRequest } from '@/uitls/api';
import { type AuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { v4 as uuidv4 } from 'uuid';
/* eslint-disable no-param-reassign */
export const authOptions: AuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        accessToken: { label: 'Access Token', type: 'text' },
        refreshToken: { label: 'Refresh Token', type: 'text' },
        tokenType: { label: 'Token Type', type: 'text' },
        accessTokenExpireOn: { label: 'Access Token Expiry', type: 'text' },
        refreshTokenExpireOn: { label: 'Refresh Token Expiry', type: 'text' },
        id: { label: 'ID', type: 'text' },
        email: { label: 'Email', type: 'text' },
        phone: { label: 'Phone', type: 'text' },
        firstName: { label: 'First Name', type: 'text' },
        lastName: { label: 'Last Name', type: 'text' },
        userRole: { label: 'User Role', type: 'text' },
        guest_id: { label: 'Guest ID', type: 'text' },
      },
      async authorize(credentials) {
        if (!credentials) return null;

        return {
          accessToken: credentials.accessToken,
          refreshToken: credentials.refreshToken,
          tokenType: credentials.tokenType,
          accessTokenExpireOn: Number(credentials.accessTokenExpireOn),
          refreshTokenExpireOn: Number(credentials.refreshTokenExpireOn),
          id: credentials.id,
          email: credentials.email,
          phone: credentials.phone,
          firstName: credentials.firstName,
          lastName: credentials.lastName,
          userRole: credentials.userRole,
          guest_id: credentials.guest_id, // include guest_id if backend sends
        };
      },
    }),
  ],
  session: { strategy: 'jwt' },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        return { ...token, ...user };
      }

      // Ensure guest_id always exists
      if (!token.guest_id) {
        token.guest_id = uuidv4();
      }

      // Handle token refresh if expired
      if (
        typeof token.accessTokenExpireOn === 'number' &&
        Date.now() > token.accessTokenExpireOn
      ) {
        try {
          const renewRes = await apiRequest({
            method: 'POST',
            url: '/auth/refresh',
            body: { refreshToken: token.refreshToken },
            tokens: token?.accessToken,
          });

          if (renewRes?.data) {
            const newTokens = renewRes.data;
            return {
              ...token,
              accessToken: newTokens.accessToken,
              refreshToken: newTokens.refreshToken,
              tokenType: newTokens.tokenType,
              accessTokenExpireOn: Date.now() + newTokens.accessTokenExpireOn,
              refreshTokenExpireOn: Date.now() + newTokens.refreshTokenExpireOn,
            };
          }
        } catch (err) {
          console.error('Token refresh failed', err);
        }
      }

      return token;
    },
    async session({ session, token }) {
      return {
        ...session,
        user: {
          id: token.id as string,
          email: token.email as string,
          phone: token.phone as string,
          firstName: token.firstName as string,
          lastName: token.lastName as string,
          userRole: token.userRole as string,
          guest_id: token.guest_id as string, // expose guest_id in session
        },
        accessToken: token.accessToken as string,
        refreshToken: token.refreshToken as string,
        tokenType: token.tokenType as string,
        accessTokenExpireOn: token.accessTokenExpireOn as number,
        refreshTokenExpireOn: token.refreshTokenExpireOn as number,
      };
    },
  },
  secret: process.env.NEXTAUTH_SECRET,
};
