'use client';

import Script from 'next/script';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  ModalBody,
  Button,
  useDisclosure,
} from '@heroui/react';
import { signIn } from 'next-auth/react';
import { useEffect, useState } from 'react';
import SocialLogin from './SocialLogin';
import * as Yup from 'yup';
import { useRouter } from 'next/navigation';

interface SignInFormValues {
  email: string;
  password: string;
}

interface ValidationErrors {
  email?: string;
  password?: string;
}

// Yup schema
const validationSchema: Yup.ObjectSchema<SignInFormValues> = Yup.object({
  email: Yup.string()
    .email('Invalid email format')
    .required('Email is required'),
  password: Yup.string().required('Password is required'),
});

export default function LoginModal({
  open,
  setOpen,
  switchToSignup,
}: {
  open: boolean;
  setOpen: (value: boolean) => void;
  switchToSignup: () => void;
}) {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const router = useRouter();
  // const authContext = useContext(AuthContext); // ✅ access context

  useEffect(() => {
    if (open) {
      onOpen();
    } else {
      onClose();
    }
  }, [open, onOpen, onClose]);

  const [values, setValues] = useState<SignInFormValues>({
    email: '',
    password: '',
  });
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [errorMessage, setErrorMessage] = useState<string>('');
  // handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const { name, value } = e.target;
    setValues(prev => ({ ...prev, [name]: value }));
  };

  const handleSignIn = async () => {
    try {
      await validationSchema.validate(values, { abortEarly: false });
      setErrors({});
      // const userData = await login(values).unwrap();
      const res = await signIn('credentials', {
        redirect: false,
        email: values.email,
        password: values.password,
      });

      if (res?.error) {
        setErrorMessage(res.error);
      } else {
        onClose();
      }
    } catch (err: unknown) {
      if (err instanceof Yup.ValidationError) {
        // collect Yup validation errors
        const validationErrors: ValidationErrors = {};
        err.inner.forEach(error => {
          if (error.path) {
            validationErrors[error.path as keyof ValidationErrors] =
              error.message;
          }
        });
        setErrors(validationErrors);
      } else {
        console.error('Sign-in failed', err);
        setErrorMessage(
          (err as any)?.data?.detail?.message || 'Invalid email or password'
        );
      }
    }
  };

  return (
    <>
      <Script
        src="https://accounts.google.com/gsi/client"
        strategy="beforeInteractive"
      />
      <Script
        src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js"
        strategy="beforeInteractive"
      />

      <Modal isOpen={isOpen} onOpenChange={setOpen} size="5xl">
        <ModalContent>
          <ModalBody>
            <div className="grid grid-cols-2 max-md:grid-cols-1">
              {/* Left section */}
              <div className="w-full p-10 space-y-5 max-md:p-4">
                <h2 className="text-3xl font-bold text-gradient1">
                  Welcome Back, Traveller!
                </h2>
                <p className="text-[#080236]">
                  Sign in to pick up right where you left off. We hope you're
                  enjoying planning your trips with Shasa!
                </p>
                <p className="text-sm text-[#080236]">
                  Still travelling without a NxVoy account?{' '}
                  <button
                    type="button"
                    className="text-blue-600 font-semibold underline"
                    onClick={switchToSignup}
                  >
                    Sign Up Here!
                  </button>
                </p>

                <div>
                  <label className="block font-semibold mb-1 text-[#1E1E76]">
                    User Name / Phone Number
                  </label>
                  <input
                    name="email"
                    type="email"
                    value={values.email}
                    onChange={handleChange}
                    placeholder="Enter Your email Id / Phone Number"
                    className="w-full px-4 py-2 rounded-full border border-gray-300 focus:ring-2 focus:ring-purple-300 outline-none"
                  />
                  {errors.email && (
                    <p className="text-red-500 text-sm">{errors.email}</p>
                  )}
                </div>
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <label className="block font-semibold mb-0 text-[#1E1E76]">
                      Password
                    </label>
                    <button
                      type="button"
                      className="text-blue-600 text-sm underline"
                      onClick={() => alert('Redirect to forgot password flow')}
                    >
                      Forgot Password?
                    </button>
                  </div>
                  <input
                    name="password"
                    type="password"
                    value={values.password}
                    onChange={handleChange}
                    placeholder="Enter Your Password"
                    className="w-full px-4 py-2 rounded-full border border-gray-300 focus:ring-2 focus:ring-purple-300 outline-none"
                  />
                  {errors.password && (
                    <p className="text-red-500 text-sm">{errors.password}</p>
                  )}
                </div>
                {errorMessage && (
                  <p className="text-sm mt-2 mb-4 text-center text-red-600">
                    {errorMessage}
                  </p>
                )}
                <Button
                  radius="full"
                  onPress={handleSignIn}
                  className="w-full bg-gradient-to-r from-purple-500 to-blue-400 text-white py-2 rounded-full font-semibold hover:opacity-90 transition-all"
                >
                  Sign In
                </Button>

                <div className="flex items-center gap-2 text-sm">
                  <input
                    type="checkbox"
                    id="remember"
                    className="accent-purple-500 mt-1"
                  />
                  <label
                    htmlFor="remember"
                    className="text-[#1E1E76] text-base font-medium"
                  >
                    Remember Me
                  </label>
                </div>
              </div>

              {/* Right section */}
              <div className="w-full bg-gray-50 flex flex-col items-center justify-center gap-4 p-10 max-md:p-4 md:border-l max-md:hidden">
                <SocialLogin
                  authType="signIn"
                  onLoginSuccess={() => {}}
                  closeModal={onClose}
                />
              </div>
            </div>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
}
