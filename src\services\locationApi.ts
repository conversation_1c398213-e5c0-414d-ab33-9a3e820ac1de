import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { getSession } from 'next-auth/react';

// Types
export interface LocationSuggestionRequest {
  query: string;
  limit?: number;
}

export interface Location {
  destination_id: number;
  name: string;
  type: string;
}

export const locationApi = createApi({
  reducerPath: 'locationApi',
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT,
    prepareHeaders: async headers => {
      // 🔑 Pull session from NextAuth instead of Redux
      const session = await getSession();

      if (session?.accessToken) {
        headers.set('authorization', `Bearer ${session.accessToken}`);
      } else if (session?.guest_id) {
        headers.set('x-guest-id', session.guest_id);
      }

      return headers;
    },
  }),
  tagTypes: ['LocationSuggestion'],
  endpoints: builder => ({
    // Location suggestions
    getLocationSuggestions: builder.mutation<
      { detail: { data: Location[] } },
      LocationSuggestionRequest
    >({
      query: params => ({
        url: '/api/v1/activities/suggestion',
        method: 'GET',
        params,
      }),
      invalidatesTags: ['LocationSuggestion'],
    }),
  }),
});

export const { useGetLocationSuggestionsMutation } = locationApi;
