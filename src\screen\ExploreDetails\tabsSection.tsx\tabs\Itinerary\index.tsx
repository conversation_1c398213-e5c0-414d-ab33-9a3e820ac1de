'use client';

import ActivitiesCard from '@/components/globalComponents/Itinerary/ActivitiesCard';
import FlightCard from '@/components/globalComponents/Itinerary/FlightCard';
import Hotelcard from '@/components/globalComponents/Itinerary/HotelCard';
import type { Flight } from '@/types/flight';
import { Accordion, AccordionItem } from '@heroui/react';
import { useState } from 'react';
import ActivityType from '../Activities/activitesType';

export default function Itinerary({
  onChangeFlightClick,
  onAddActivityClick,
}: {
  onChangeFlightClick?: () => void;
  onAddActivityClick?: () => void;
}) {
  const defaultContent =
    'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.';

  const days = [
    {
      id: '1',
      title: 'Day 1',
      extra: 'Arrival In Germany : 1 flight 1 hotel 1 transfer',
      contentType: 'flight',
    },
    {
      id: '2',
      title: 'Day 2',
      extra: 'City Tour : 2 attractions',
      contentType: 'text',
    },
    {
      id: '3',
      title: 'Day 3',
      extra: 'Departure : 1 transfer',
      contentType: 'text',
    },
  ];

  const flightData: Flight = {
    id: '6E7352-A2-g421',
    from: 'United Kingdom',
    to: 'Germany',
    duration: '5 hr 5 min',
    airlines: [
      {
        name: 'IndiGo',
        logo: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/hotel.png',
      },
      {
        name: 'Air India',
        logo: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/airindia.png',
      },
    ],
    departure: {
      time: '02:18',
      date: 'Thu, 25 Feb',
      location: 'United Kingdom',
    },
    stops: '1 stop',
    stopDetails: 'HYD 1 hr 50 min',
    arrival: {
      time: '06:35',
      date: 'Thu, 25 Feb',
      location: 'Germany',
    },
    baggage: {
      cabin: '7kgs',
      checkin: '30kgs',
    },
  };

  // First item open by default
  const [openItems, setOpenItems] = useState<string[]>(['1']);

  const handleChange = (keys: string[]) => {
    setOpenItems(keys);
  };

  return (
    <Accordion
      selectedKeys={openItems}
      showDivider={false}
      onSelectionChange={keys => handleChange(Array.from(keys) as string[])}
    >
      {days.map(day => (
        <AccordionItem
          key={day.id}
          aria-label={day.title}
          title={
            openItems.includes(day.id)
              ? `${day.title} – ${day.extra}`
              : day.title
          }
          classNames={{
            heading: 'bg-[#F0F0FF] text-white rounded-lg mb-[10px]',
            trigger: 'bg-[#F0F0FF] text-white px-4 py-2 rounded-lg',
            content: 'bg-white p-4 rounded-lg',
          }}
        >
          {day.contentType === 'flight' ? (
            <div className="flex flex-col gap-5">
              <FlightCard
                flight={flightData}
                onChangeClick={onChangeFlightClick}
              />

              <Hotelcard flight={flightData} />
              {/* <ActivitiesCard flight={flightData} /> */}
              <ActivityType
                flight={flightData}
                onAddActivityClick={onAddActivityClick}
              />
              <ActivitiesCard flight={flightData} />
            </div>
          ) : (
            <p>{defaultContent}</p>
          )}
        </AccordionItem>
      ))}
    </Accordion>
  );
}
