// src/slices/threadSlice.ts
import type { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@reduxjs/toolkit';

interface ThreadState {
  thread_id: string | null;
  expires_at: string | null;
  created_at: string | null;
  reused: boolean;
  usage: string | null;
}

const initialState: ThreadState = {
  thread_id: null,
  expires_at: null,
  created_at: null,
  reused: false,
  usage: null,
};

const threadSlice = createSlice({
  name: 'thread',
  initialState,
  reducers: {
    setThread(state, action: PayloadAction<ThreadState>) {
      return { ...state, ...action.payload };
    },
    resetThread() {
      return initialState;
    },
  },
});

export const { setThread, resetThread } = threadSlice.actions;
export default threadSlice.reducer;
