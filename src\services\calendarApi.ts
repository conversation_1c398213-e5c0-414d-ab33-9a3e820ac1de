// src/services/calendarApi.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { getSession } from 'next-auth/react';

interface CalendarRequest {
  startDate: string;
  endDate: string;
  token?: string;
  guest_id?: string;
}

interface CalendarResponse {
  message: string;
  detail: {
    data: {
      date: string;
      range: string;
    }[];
  };
}

export const calendarApi = createApi({
  reducerPath: 'calendarApi',
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT,
    prepareHeaders: async headers => {
      const session = await getSession();

      if (session?.accessToken) {
        headers.set('authorization', `Bearer ${session.accessToken}`);
      } else if (session?.user?.guest_id) {
        headers.set('x-guest-id', session.user.guest_id);
      }

      return headers;
    },
  }),
  tagTypes: ['CalendarDatesList'],
  endpoints: builder => ({
    getDatesList: builder.mutation<CalendarResponse, CalendarRequest>({
      query: ({ startDate, endDate }) => ({
        url: `/api/v1/flight/fare-calendar`,
        method: 'GET',
        params: {
          source: 'LHR',
          destination: 'JFK',
          startDate,
          endDate,
        },
      }),
      invalidatesTags: ['CalendarDatesList'],
    }),
  }),
});

export const { useGetDatesListMutation } = calendarApi;
