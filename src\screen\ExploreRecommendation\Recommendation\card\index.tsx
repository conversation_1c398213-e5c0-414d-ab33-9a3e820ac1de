import { MapPointIcon, CalendarIcon } from '@/components/icons';
import { Card, CardBody, Button } from '@heroui/react';
import { useState } from 'react';
import { Image } from '@heroui/image';
import Link from 'next/link';

interface Recommendation {
  title: string;
  duration: string;
  location: string;
  tags: string;
  image_url: string;
  badge: string;
  random_images?: string[];
  imagesm1: string;
  imagesm2: string;
  imagesm3: string;
  imagesm4: string;
  city: string;
  description: string;
  theme: string[];
  rating: number;
  review_count: number;
  sample_experiences: string[];
  destination_id?: number;
}

interface RecommendationCardProps {
  recommendations: Recommendation;
}

const RecommendationCard = ({ recommendations }: RecommendationCardProps) => {
  const [expanded, setExpanded] = useState<number | null>(null);

  const isLong = recommendations.description.length > 170;
  const isExpanded = expanded === recommendations.destination_id;
  const displayText =
    isExpanded || !isLong
      ? recommendations.description
      : `${recommendations.description.slice(0, 170)}...`;

  return (
    <div className="flex flex-col gap-2 flex-1 overflow-hidden mb-3">
      <Card
        key={`${recommendations.title}-${recommendations.location}-${recommendations.duration}`}
        className="bg-white border-none shadow-none  transition-all duration-300 ease-in-out flex-shrink-0"
        data-card="recommendation"
      >
        <CardBody>
          <div className="flex flex-row max-md:flex-col justify-between rounded-xl w-full">
            {/* Left section: Image and details */}
            <div className="flex flex-row max-md:flex-col gap-4">
              <div className="flex flex-col gap-1.5 flex-2">
                {/* Large Image */}
                <div className="w-full">
                  <Image
                    src={recommendations.image_url}
                    alt="Large Image"
                    width={350}
                    height={170}
                    radius="none"
                    className="object-cover rounded-tl-lg rounded-tr-xl min-w-full max-w-fit w-full h-full"
                  />
                </div>

                {/* Small Image Grid */}
                <div
                  // className="grid grid-cols-4 gap-1.5"
                  className={`grid gap-1.5 w-full ${
                    (recommendations?.random_images?.length ?? 0) >= 4
                      ? 'grid-cols-4'
                      : `grid-cols-${recommendations?.random_images?.length ?? 1}`
                  }`}
                >
                  {recommendations?.random_images?.map((src, index) =>
                    src ? (
                      <Image
                        key={src} // ✅ use src as unique key
                        src={src}
                        alt="Recommendation Image"
                        width={100}
                        height={50}
                        radius="none"
                        className={`w-full aspect-square object-cover ${
                          index === 0 ? 'rounded-bl-lg' : ''
                        } ${
                          index ===
                            (recommendations?.random_images?.length ?? 0) - 1 &&
                          (recommendations?.random_images?.length ?? 0) <= 4
                            ? 'rounded-br-lg'
                            : ''
                        }`}
                      />
                    ) : null
                  )}

                  {/* Blurred Last Image with Button */}

                  {
                    // recommendations?.random_images?.length
                    (recommendations?.random_images?.length ?? 0) > 4 ? (
                      <div className="relative">
                        <Image
                          src={recommendations.image_url}
                          alt="Small Image 4 (Blurred)"
                          width={100}
                          height={50}
                          radius="none"
                          className="w-full aspect-square object-cover rounded-br-lg filter blur-xs"
                        />
                        <div className="absolute inset-0 flex items-center z-[99999] justify-center text-sm text-white">
                          <Button
                            type="button"
                            size="sm"
                            className="bg-white text-black text-sm px-4 py-1 rounded-full shadow hover:bg-gray-200"
                            variant="flat"
                          >
                            View All
                          </Button>
                        </div>
                      </div>
                    ) : null
                  }
                </div>
              </div>

              {/* Text content */}
              <div className="text-sm space-y-0.5 flex-4">
                <p className="font-medium text-lg">{recommendations.title}</p>
                <div className="flex flex-row gap-3 py-2">
                  <div className="flex flex-row gap-1 items-center">
                    <CalendarIcon
                      size={14}
                      isAnimation={false}
                      className="text-black"
                    />
                    <p className="text-md">{recommendations.duration}</p>
                  </div>

                  <div className="flex flex-row gap-1 items-center">
                    <MapPointIcon
                      size={14}
                      isAnimation={false}
                      className="text-black"
                    />
                    <p className="text-md">{recommendations?.city}</p>
                  </div>
                </div>

                <div>
                  <p className="text-default-700 text-md">
                    {displayText}
                    {/* {isLong && (
                      <button
                        type="button"
                        onClick={() =>
                          setExpanded(
                            isExpanded
                              ? null
                              : recommendations?.destination_id || 0
                          )
                        }
                        className="mt-2 text-blue-600 font-medium hover:underline"
                      >
                        {isExpanded ? 'View Less ↑' : 'View More →'}
                      </button>
                    )} */}
                  </p>
                </div>

                <p className="text-md font-medium py-2">
                  {recommendations.theme.join(', ')}
                </p>

                <p className="text-md text-default-700">
                  {recommendations.sample_experiences.join(', ')}
                </p>
              </div>
            </div>

            {/* Right section: Action button */}
            <div className="flex flex-col gap-2 justify-between text-right">
              <p className="flex items-center gap-2">
                <span className="text-base font-bold">
                  {recommendations.rating}
                </span>
                <span className="text-default-700 font-medium text-sm whitespace-nowrap">
                  ({recommendations.review_count}) Ratings
                </span>
              </p>

              <div className="text-right">
                <Link
                  href={`/explore/${recommendations.location}/${recommendations.title}`}
                >
                  <Button
                    type="button"
                    color="primary"
                    size="sm"
                    className="font-semibold rounded-full bg-primary-200 text-white"
                  >
                    Book Trip
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default RecommendationCard;
