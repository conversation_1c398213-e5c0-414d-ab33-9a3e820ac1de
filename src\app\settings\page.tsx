'use client';

import { useState } from 'react';

export default function SettingsPage() {
  const [adults, setAdults] = useState(1);
  const [childrenCount, setChildrenCount] = useState(0);
  const [infants, setInfants] = useState(0);
  const [elderly, setElderly] = useState(0);

  const [showInfant, setShowInfant] = useState(false);
  const [showElderly, setShowElderly] = useState(false);

  // PriceRangeSlider state
  const [range, setRange] = useState<[number, number]>([0, 10000]);
  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <p>Settings</p>
    </div>
  );
}
