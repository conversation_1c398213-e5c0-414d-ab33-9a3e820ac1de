module.exports = {
  env: {
    browser: true,
    es2020: true,
    node: true,
  },
  ignorePatterns: [
    'src/components/ui/**', // ignore all shadcn UI components
  ],
  extends: [
    'airbnb',
    'plugin:import/recommended',
    'plugin:import/typescript',
    'plugin:@typescript-eslint/eslint-recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:import/errors',
    'plugin:import/warnings',
    'plugin:import/typescript',
    'plugin:jsx-a11y/recommended',
    'plugin:@next/next/recommended',
    'prettier',
    'plugin:prettier/recommended',
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
    },
    ecmaVersion: 2021,
    sourceType: 'module',
  },
  plugins: [
    'import',
    '@typescript-eslint',
    'react',
    'simple-import-sort',
    'prettier',
  ],
  root: true,
  rules: {
    'import/no-unresolved': 'off',
    'import/namespace': 'off',
    'import/no-extraneous-dependencies': 'off',
    'prettier/prettier': ['error', { endOfLine: 'auto' }],
    '@typescript-eslint/ban-ts-comment': 'off',
    '@typescript-eslint/consistent-type-imports': 'error',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-use-before-define': ['error'],
    'consistent-return': 'error',
    'import/extensions': 'off',
    'import/no-cycle': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    'react/require-default-props': 'off',
    'import/no-relative-packages': 'off',
    'import/order': 'off',
    'import/no-duplicates': 'off',
    'import/default': 'off',
    'react/no-array-index-key': 'off',
    'import/no-self-import': 'off',
    'import/no-named-as-default': 'off',
    'import/no-named-as-default-member': 'off',
    'import/prefer-default-export': 'off',
    'jsx-a11y/anchor-is-valid': [
      'error',
      {
        aspects: ['invalidHref', 'preferButton'],
        components: ['Link'],
        specialLink: ['hrefLeft', 'hrefRight'],
      },
    ],
    'jsx-a11y/label-has-associated-control': 'off',
    'no-console': 'warn',
    'no-underscore-dangle': 'off',
    'no-use-before-define': 'error',
    'react/jsx-filename-extension': [1, { extensions: ['.ts', '.tsx'] }],
    'react/jsx-props-no-spreading': 'off',
    'react/no-unescaped-entities': 'off',
    'react/prop-types': 'off',
    'react/react-in-jsx-scope': 'off',
    // 'simple-import-sort/exports': 'error',
    // 'simple-import-sort/imports': 'error',
    'sort-keys': 'off',
    'no-unused-vars': 'error',
    'react/function-component-definition': [
      2,
      {
        namedComponents: ['arrow-function', 'function-declaration'],
        unnamedComponents: 'arrow-function',
      },
    ],

    camelcase: [
      'error',
      {
        properties: 'never',
        ignoreDestructuring: true,
        ignoreImports: true,
        ignoreGlobals: true,
      },
    ],
  },
  settings: {
    // TypeScript needs this to resolve nextjs absolute imports
    'import/resolver': {
      typescript: {
        alwaysTryTypes: true,
        project: './tsconfig.json',
      },
      node: {
        extensions: ['.js', '.jsx', '.ts', '.tsx'],
        moduleDirectory: ['node_modules', 'src'],
      },
    },
    react: {
      version: 'detect',
    },
  },
  overrides: [
    {
      files: ['**/*.ts', '**/*.tsx'],
      rules: {
        'import/no-unresolved': 'off',
        'import/namespace': 'off',
        'import/no-extraneous-dependencies': 'off',
        'import/no-self-import': 'off',
        'import/no-relative-packages': 'off',
        'import/default': 'off',
        'import/no-useless-path-segments': 'off',
        'react/jsx-props-no-spreading': 'off',
        'react/jsx-no-constructed-context-values': 'off',
        'react/no-array-index-key': 'warn',
        '@typescript-eslint/no-unused-vars': 'warn',
        'no-unused-vars': 'warn',
      },
    },
    {
      files: ['**/slices/**/*.ts', '**/slices/**/*.tsx'],
      rules: {
        'no-param-reassign': ['error', { props: false }],
      },
    },
  ],
};
