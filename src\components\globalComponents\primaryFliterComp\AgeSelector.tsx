'use client';

import { useDispatch, useSelector } from 'react-redux';
import {
  setAdults,
  setChildren,
  setInfants,
  setElderly,
  toggleShowInfant,
  toggleShowElderly,
} from '@/slices/travellersSlice';
import type { RootState } from '@/store/authStore';

export default function AgeSelector() {
  const dispatch = useDispatch();

  // ✅ Pull values from Redux
  const { adults, children, infants, elderly, showInfant, showElderly } =
    useSelector((state: RootState) => state.travellers);

  const renderOptions = (
    max: number,
    current: number,
    setFn: (n: number) => void
  ) => (
    <div className="flex gap-4 mt-2">
      {Array.from({ length: max + 1 }, (_, i) => (
        <button
          key={i}
          type="button"
          onClick={() => setFn(i)}
          className={`w-8 h-8 rounded-md border text-sm font-medium ${
            current === i
              ? 'border-primary-200 text-primary-200 bg-primary-200/10'
              : 'border-transparent text-gray-700 hover:bg-gray-100'
          }`}
        >
          {i}
        </button>
      ))}
      <button
        type="button"
        onClick={() => setFn(max + 1)}
        className={`text-sm font-medium px-2 rounded-md ${
          current > max
            ? 'border border-primary-200 text-primary-200 bg-primary-200/10'
            : 'text-gray-700 hover:bg-gray-100'
        }`}
      >
        &gt;{max}
      </button>
    </div>
  );

  return (
    <div className="rounded-2xl bg-white p-6 w-full max-w-sm space-y-5">
      {/* Adults */}
      <div>
        <label className="text-gray text-sm ">
          Adults <span className="text-gray-500">(12y +)</span>
        </label>
        {renderOptions(6, adults, val => dispatch(setAdults(val)))}
      </div>

      {/* Children */}
      <div>
        <label className="text-gray text-sm">
          Children <span className="text-gray-500">(2y - 12y)</span>
        </label>
        {renderOptions(5, children, val => dispatch(setChildren(val)))}
      </div>

      {/* Info notes */}
      <div className="pt-2 text-sm text-gray space-y-4 justify-end text-end">
        <div>
          <button
            type="button"
            onClick={() => dispatch(toggleShowInfant())}
            className="cursor-pointer bg-transparent border-none p-0 text-gray hover:underline"
          >
            + infant (below 2y)
          </button>
          {showInfant &&
            renderOptions(3, infants, val => dispatch(setInfants(val)))}
        </div>

        <div>
          <button
            type="button"
            onClick={() => dispatch(toggleShowElderly())}
            className="cursor-pointer bg-transparent border-none p-0 text-gray hover:underline"
          >
            + elderly (above 60y)
          </button>
          {showElderly &&
            renderOptions(4, elderly, val => dispatch(setElderly(val)))}
        </div>
      </div>
    </div>
  );
}
