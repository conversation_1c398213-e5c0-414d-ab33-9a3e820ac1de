import { useEffect } from 'react';
import { useDisclosure } from '@heroui/react';
import { SortIcon } from '@/components/icons';
import RecommendationCard from './card';
import { useSession } from 'next-auth/react';
import Cookies from 'js-cookie';
import { useDispatch, useSelector } from 'react-redux';
import {
  setRecommendations,
  toggleType,
  setCity,
  applyFilters,
  resetFilters,
  selectFilteredRecommendations,
  selectFilterTypes,
  selectSelectedTypes,
  selectCities,
  selectSelectedCity,
} from '@/slices/recommendationSlice';
import { useGetRecommendationsMutation } from '@/services/recommendationApi';
import RecommendationFilter from '@/components/globalComponents/Filter/RecommendationFilter';

const Recommendation = () => {
  const { data: session } = useSession() ?? { data: null };
  const dispatch = useDispatch();

  // 🔥 Use RTK Query mutation hook
  const [getRecommendations, { isLoading }] = useGetRecommendationsMutation();

  // 🔥 Use slice selectors
  const recommendations = useSelector(selectFilteredRecommendations);

  const { isOpen, onOpen, onClose } = useDisclosure();

  // Fetch data on mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getRecommendations({
          page: 1,
          pageSize: 10,
        }).unwrap();

        if (res.detail.data.recommendations) {
          dispatch(setRecommendations(res.detail.data.recommendations));
        }
      } catch (err) {
        dispatch(setRecommendations([]));
      }
    };

    fetchData();
  }, [dispatch, getRecommendations, Cookies.get('guest_id'), session?.user]);

  useEffect(() => {
    if (!isOpen) {
      onClose();
    }
  }, [onOpen, onClose]);

  // Apply filters from slice
  const filterData = () => {
    dispatch(applyFilters());
    onClose();
  };

  return (
    <div className="relative">
      {/* Header */}
      <div className="flex flex-row items-center justify-between flex-shrink-0 md:sticky md:top-0 md:z-50 mb-2 bg-[#F2F2FF]">
        <div>
          <p className="text-lg font-bold">Recommendation</p>
        </div>
        <div className="flex flex-row items-center gap-2 cursor-pointer">
          <SortIcon isAnimation={false} className="text-default-Secondary" />
          <span
            role="button"
            tabIndex={0}
            onClick={onOpen}
            onKeyDown={e => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                onOpen();
              }
            }}
            className="text-sm font-medium text-default-Secondary cursor-pointer"
          >
            Filter
          </span>
        </div>
      </div>

      {/* Loading state */}
      {isLoading && <p>Loading recommendations...</p>}

      {/* List */}
      {recommendations?.map(item => (
        <RecommendationCard key={item.title} recommendations={item} />
      ))}

      {/* this is the RecommendationFilter use this insted of modal */}
      <RecommendationFilter
        open={isOpen}
        onClose={onClose}
        onApply={filterData}
      />
    </div>
  );
};

export default Recommendation;
