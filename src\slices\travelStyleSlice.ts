/* eslint-disable no-param-reassign */
import type { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@reduxjs/toolkit';

interface TravelStyleState {
  selectedTags: string[];
}

const initialState: TravelStyleState = {
  selectedTags: [],
};

const travelStyleSlice = createSlice({
  name: 'travelStyle',
  initialState,
  reducers: {
    toggleTag(state, action: PayloadAction<string>) {
      const tag = action.payload;
      if (state.selectedTags.includes(tag)) {
        state.selectedTags = state.selectedTags.filter(t => t !== tag);
      } else {
        state.selectedTags.push(tag);
      }
    },
    setSelectedTags(state, action: PayloadAction<string[]>) {
      state.selectedTags = action.payload;
    },
    resetTags(state) {
      state.selectedTags = [];
    },
  },
});

export const { toggleTag, setSelectedTags, resetTags } =
  travelStyleSlice.actions;
export default travelStyleSlice.reducer;

// Selectors
export const selectSelectedTags = (state: { travelStyle: TravelStyleState }) =>
  state.travelStyle.selectedTags;
