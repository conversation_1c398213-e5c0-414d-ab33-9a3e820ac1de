// src/store/authStore.ts
import { configureStore, combineReducers } from '@reduxjs/toolkit';
import {
  persistReducer,
  persistStore,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from 'redux-persist';
import storage from 'redux-persist/lib/storage';

import { userApi } from '@/services/userApi';
import { dummyApi } from '@/services/dummyApi';
import { authApi } from '@/services/authApi';
import { locationApi } from '@/services/locationApi';
import { recommendationApi } from '@/services/recommendationApi';
import { threadApi } from '@/services/threadApi';
import { travelStyleApi } from '@/services/travelStyleApi';
import { calendarApi } from '@/services/calendarApi';
import { cityApi } from '@/services/citydetailsApi';

import authReducer from '@/slices/authSlice';
import recommendationReducer from '@/slices/recommendationSlice';
import flightSearchReducer from '@/slices/flightSearchSlice';
import locationReducer from '@/slices/locationSlice';
import threadReducer from '@/slices/threadSlice';
import travelStyleReducer from '@/slices/travelStyleSlice';
import calendarReducer from '@/slices/calendarSlice';
import travellersReducer from '@/slices/travellersSlice';
import budgetReducer from '@/slices/budgetSlice';

// ✅ Combine all reducers
const rootReducer = combineReducers({
  flightSearchForm: flightSearchReducer,
  auth: authReducer,
  recommendations: recommendationReducer,
  location: locationReducer,
  thread: threadReducer,
  travelStyle: travelStyleReducer,
  calendar: calendarReducer,
  travellers: travellersReducer, // 👈 add this
  budget: budgetReducer,

  [userApi.reducerPath]: userApi.reducer,
  [dummyApi.reducerPath]: dummyApi.reducer,
  [authApi.reducerPath]: authApi.reducer,
  [recommendationApi.reducerPath]: recommendationApi.reducer,
  [locationApi.reducerPath]: locationApi.reducer,
  [threadApi.reducerPath]: threadApi.reducer,
  [travelStyleApi.reducerPath]: travelStyleApi.reducer,
  [calendarApi.reducerPath]: calendarApi.reducer,
  [cityApi.reducerPath]: cityApi.reducer,
});

// ✅ Persist config
const persistConfig = {
  key: 'root',
  storage,
  whitelist: [
    'auth',
    'flightSearchForm',
    'location',
    'travelStyle',
    'calendar',
    'travellers',
    'budget',
  ], // 👈 persist only what you need
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

// ✅ Store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    })
      .concat(userApi.middleware)
      .concat(dummyApi.middleware)
      .concat(authApi.middleware)
      .concat(recommendationApi.middleware)
      .concat(locationApi.middleware)
      .concat(threadApi.middleware)
      .concat(travelStyleApi.middleware)
      .concat(calendarApi.middleware)
      .concat(cityApi.middleware),
});

// ✅ Persistor
export const persistor = persistStore(store);

// Types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
