import type { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@reduxjs/toolkit';
import type { RootState } from '@/store/authStore';

export interface User {
  id: string;
  firstname?: string;
  lastname?: string;
  email?: string;
  guest_id?: string;
}

export interface AuthState {
  token: string | null;
  refreshToken: string | null;
  user: User | null;
  isAuthenticated: boolean;
  expiry?: number | null;
}

// Safe storage helpers
const getLocal = (key: string) =>
  typeof window !== 'undefined' ? localStorage.getItem(key) : null;

const parseJSON = (str: string | null) => {
  if (!str) return null;
  try {
    return JSON.parse(str);
  } catch {
    return null;
  }
};

const expiry = getLocal('expiry');
const isExpired = expiry ? Date.now() > parseInt(expiry, 10) : true;

const initialState: AuthState = {
  token: !isExpired ? getLocal('token') : null,
  refreshToken: !isExpired ? getLocal('refreshToken') : null,
  user: !isExpired ? parseJSON(getLocal('user')) : null,
  isAuthenticated: !isExpired && !!getLocal('token'),
  expiry: !isExpired ? parseInt(expiry!, 10) : null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setCredentials: (
      _,
      action: PayloadAction<{ token: string; refreshToken: string; user: User }>
    ): AuthState => {
      const expiryTime = Date.now() + 15 * 60 * 1000; // 15 min
      console.log('action.payload', action.payload);
      if (typeof window !== 'undefined') {
        localStorage.setItem('token', action.payload.token);
        localStorage.setItem('refreshToken', action.payload.refreshToken);
        localStorage.setItem('user', JSON.stringify(action.payload.user));
        localStorage.setItem('expiry', expiryTime.toString());
      }

      return {
        token: action.payload.token,
        refreshToken: action.payload.refreshToken,
        user: action.payload.user,
        isAuthenticated: true,
        expiry: expiryTime,
      };
    },
    clearCredentials: (): AuthState => {
      if (typeof window !== 'undefined') {
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('user');
        localStorage.removeItem('expiry');
        localStorage.removeItem('guest_id');
      }
      return {
        token: null,
        refreshToken: null,
        user: null,
        isAuthenticated: false,
        expiry: null,
      };
    },
    setGuestCredentials: (
      _,
      action: PayloadAction<{ guest_id: string }>
    ): AuthState => {
      if (typeof window !== 'undefined') {
        localStorage.setItem('guest_id', action.payload.guest_id);
      }
      return {
        token: null,
        refreshToken: null,
        user: null,
        isAuthenticated: false,
        expiry: null,
      };
    },
  },
});

export const selectIsAuthenticated = (state: RootState) =>
  state.auth.isAuthenticated;
// Selectors
export const selectCurrentUser = (state: { auth: AuthState }) =>
  state.auth.user;
export const { setCredentials, clearCredentials, setGuestCredentials } =
  authSlice.actions;
export default authSlice.reducer;
