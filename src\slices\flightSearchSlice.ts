// src/slices/flightSearchSlice.ts
import type { FlightSearchForm } from '@/types/flights';
import type { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@reduxjs/toolkit';
import { format } from 'date-fns';

const initialState: FlightSearchForm = {
  trip_type: 'One Way',
  travel_class: 'Direct',
  adults: 1,
  children: 0,
  infants: 0,
  departure_date: format(new Date(), 'yyyy-MM-dd'),
  return_date: format(new Date(), 'yyyy-MM-dd'),
  destination: { city: '', iata_code: '' },
  origin: { city: '', iata_code: '' },
  direct_only: false,
};

const flightSearchSlice = createSlice({
  name: 'flightSearchForm',
  initialState,
  reducers: {
    updateTripConfig: (
      state,
      action: PayloadAction<{ trip_type?: string; travel_class?: string }>
    ): FlightSearchForm => ({
      ...state,
      ...(action.payload.trip_type !== undefined && {
        trip_type: action.payload.trip_type,
      }),
      ...(action.payload.travel_class !== undefined && {
        travel_class: action.payload.travel_class,
      }),
    }),

    updateDirectFlightSearch: (
      state,
      action: PayloadAction<{ direct_only: boolean }>
    ): FlightSearchForm => ({
      ...state,
      direct_only: action.payload.direct_only,
    }),

    updatePassengers: (
      state,
      action: PayloadAction<{
        adults?: number;
        childrenCount?: number;
        infants?: number;
      }>
    ): FlightSearchForm => ({
      ...state,
      ...(action.payload.adults !== undefined && {
        adults: Math.max(1, action.payload.adults),
      }),
      ...(action.payload.childrenCount !== undefined && {
        children: Math.max(0, action.payload.childrenCount),
      }),
      ...(action.payload.infants !== undefined && {
        infants: Math.max(0, action.payload.infants),
      }),
    }),

    updateDates: (
      state,
      action: PayloadAction<{ departure_date?: string; return_date?: string }>
    ): FlightSearchForm => ({
      ...state,
      ...(action.payload.departure_date !== undefined && {
        departure_date: action.payload.departure_date,
      }),
      ...(action.payload.return_date !== undefined && {
        return_date: action.payload.return_date,
      }),
    }),

    updateLocations: (
      state,
      action: PayloadAction<{
        origin?: { city: string; iata_code: string };
        destination?: { city: string; iata_code: string };
      }>
    ): FlightSearchForm => ({
      ...state,
      ...(action.payload.origin !== undefined && {
        origin: action.payload.origin,
      }),
      ...(action.payload.destination !== undefined && {
        destination: action.payload.destination,
      }),
    }),

    setFlightSearchForm: (
      state,
      action: PayloadAction<Partial<FlightSearchForm>>
    ): FlightSearchForm => ({
      ...state,
      ...action.payload,
    }),

    resetFlightSearchForm: (): FlightSearchForm => ({
      ...initialState,
      departure_date: format(new Date(), 'yyyy-MM-dd'),
      return_date: format(new Date(), 'yyyy-MM-dd'),
    }),
  },
});

// Actions
export const {
  updateTripConfig,
  updatePassengers,
  updateDates,
  updateLocations,
  updateDirectFlightSearch,
  setFlightSearchForm,
  resetFlightSearchForm,
} = flightSearchSlice.actions;

// Reducer
export default flightSearchSlice.reducer;

// Selectors
export const selectFlightSearchForm = (state: {
  flightSearchForm: FlightSearchForm;
}) => state.flightSearchForm;

export const selectTripType = (state: { flightSearchForm: FlightSearchForm }) =>
  state.flightSearchForm.trip_type;

export const selectTravelClass = (state: {
  flightSearchForm: FlightSearchForm;
}) => state.flightSearchForm.travel_class;

export const selectPassengerCounts = (state: {
  flightSearchForm: FlightSearchForm;
}) => ({
  adults: state.flightSearchForm.adults,
  children: state.flightSearchForm.children,
  infants: state.flightSearchForm.infants,
});

export const selectTotalPassengers = (state: {
  flightSearchForm: FlightSearchForm;
}) =>
  state.flightSearchForm.adults +
  state.flightSearchForm.children +
  state.flightSearchForm.infants;

export const selectOrigin = (state: { flightSearchForm: FlightSearchForm }) =>
  state.flightSearchForm.origin;

export const selectDestination = (state: {
  flightSearchForm: FlightSearchForm;
}) => state.flightSearchForm.destination;

export const selectDates = (state: { flightSearchForm: FlightSearchForm }) => ({
  departure_date: state.flightSearchForm.departure_date,
  return_date: state.flightSearchForm.return_date,
});
