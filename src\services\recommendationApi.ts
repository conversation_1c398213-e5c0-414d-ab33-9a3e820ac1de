// src/services/recommendationApi.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

interface Recommendation {
  title: string;
  duration: string;
  location: string;
  tags: string;
  image: string;
  imagesm1: string;
  imagesm2: string;
  imagesm3: string;
  imagesm4: string;
  badge: string;
  type: string | string[];
  city: string;
  country: string;
  image_url: string;
  theme: string[];
  description: string;
  rating: number;
  review_count: number;
  sample_experiences: [];
}

export const recommendationApi = createApi({
  reducerPath: 'recommendationApi',
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT,
  }),
  tagTypes: ['Recommendations'],
  endpoints: builder => ({
    getRecommendations: builder.mutation<
      { detail: { data: { recommendations: Recommendation[] } } },
      // eslint-disable-next-line camelcase
      { page?: number; pageSize?: number; token?: string; guest_id?: string }
    >({
      query: ({ page = 1, pageSize = 10, token, guest_id }) => {
        const headers: Record<string, string> = {};
        if (token) headers.authorization = `Bearer ${token}`;
        // eslint-disable-next-line camelcase
        if (guest_id) headers['x-guest-id'] = guest_id;

        return {
          url: `/api/v1/search/recommendation?display_type=recommendations&page=${page}&page_size=${pageSize}`,
          method: 'POST',
          body: {}, // empty body
          headers,
        };
      },
      invalidatesTags: ['Recommendations'],
    }),
  }),
});

export const { useGetRecommendationsMutation } = recommendationApi;
export type { Recommendation };
