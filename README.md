# NxVoy Holiday Planner FE

.

.

.

## Features

- 🚀 **Performance Optimized** - 100% Lighthouse score out of the box
- 🔍 **SEO Ready** - Comprehensive meta tags and structured data
- 🎨 **Modern UI** - Clean design with light/dark mode support
- 🛠️ **Developer Experience** - Comprehensive tooling preconfigured
- 📱 **Responsive** - Mobile-first approach for all screen sizes
- ♿ **Accessible** - WCAG compliance with a11y best practices
- 🔒 **Type Safe** - Full TypeScript support throughout the codebase

## 📚 Tech Stack

- [`Next.js 15`](https://nextjs.org/) - React framework for production
- [`TypeScript`](https://typescriptlang.org) - Type safety and improved developer experience
- [`Tailwind CSS`](https://tailwindcss.com/) - Utility-first CSS framework
- [`ESLint`](https://eslint.org/) - Code quality and consistency
- [`Prettier`](https://prettier.io/) - Code formatting
- [`Husky`](https://github.com/typicode/husky) - Git hooks for pre-commit validation
- [`next-themes`](https://github.com/pacocoursey/next-themes) - Theme management (light/dark mode)
- [`React Query`](https://tanstack.com/query) - Data fetching and state management

## 🔍 SEO

- Comprehensive meta tags in the layout file
- OpenGraph and Twitter card metadata
- `/public/robots.txt` configured to allow indexing
- Sitemap generation support

## 🚀 Getting Started

### Create a new project using this boilerplate

```bash
git clone
```

### Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

Your application will be available at [http://localhost:3000](http://localhost:3000).

## 📋 Scripts

| Command       | Description                      |
| ------------- | -------------------------------- |
| `npm dev`     | Start development server         |
| `npm build`   | Build production application     |
| `npm start`   | Start production server          |
| `npm lint`    | Run ESLint to check code quality |
| `npm analyze` | Generate bundle analyzer report  |
| `npm format`  | Format code with Prettier        |

## 🧰 Project Structure

```
src/
├── app/              # App router pages
├── components/       # Reusable UI components
├── hooks/            # Custom React hooks
├── lib/              # Utility functions and libraries
├── providers/        # Context providers
├── styles/           # Global styles
├── types/            # TypeScript type definitions
└── utils/            # Helper functions
```

## 💅 Styling

This boilerplate uses Tailwind CSS for styling with a custom theme configuration that supports light and dark modes. Styled Components are generated at build time and served with the document generated by Next.js Server Side Rendering for optimal performance.

The project includes Google Fonts (Inter and Poppins) preloaded at build time for improved performance and consistent typography.

## 🧩 Development Features

### Absolute Imports

Absolute imports are configured with the `@/` prefix starting from the `src` folder:

```tsx
// Instead of this
import { Button } from '../../../components/ui/Button';

// You can write this
import { Button } from '@/components/ui/Button';
```

### Import Sorting

Imports are automatically grouped and sorted in the following order:

1. External dependencies
2. Absolute imports (`@/*`)
3. Relative imports

This is implemented using [`eslint-plugin-simple-import-sort`](https://github.com/lydell/eslint-plugin-simple-import-sort).

### Code Quality

ESLint extends:

- [`eslint-config-airbnb`](https://github.com/airbnb/javascript/tree/master/packages/eslint-config-airbnb)
- [`eslint-config-next`](https://www.npmjs.com/package/eslint-config-next)
- [`eslint-plugin-jsx-a11y`](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y) for accessibility recommendations

### Git Hooks

On every commit, staged files are validated with ESLint using Husky and lint-staged:

```json
"husky": {
  "hooks": {
    "pre-commit": "lint-staged"
  }
},
"lint-staged": {
  "*.{js,jsx,ts,tsx}": "eslint --cache --fix --cache-location ./node_modules/.cache/.eslintcache"
}
```

## Commit Message Rules

This project uses **[commitlint](https://github.com/conventional-changelog/commitlint)** with a custom configuration based on the [Conventional Commits](https://www.conventionalcommits.org/) specification.

### Rules

| Rule                | Level | Condition | Value / Description                                  |
| ------------------- | ----- | --------- | ---------------------------------------------------- |
| `header-max-length` | 2     | always    | The commit header must not exceed **70 characters**  |
| `subject-case`      | 2     | always    | The subject line must be in **lower-case**           |
| `type-case`         | 2     | always    | The type must be in **lower-case**                   |
| `type-enum`         | 2     | always    | The type must be one of the following allowed values |

### Allowed Commit Types

| Type       | Description                                               |
| ---------- | --------------------------------------------------------- |
| `feat`     | A new feature                                             |
| `fix`      | A bug fix                                                 |
| `docs`     | Documentation only changes                                |
| `style`    | Changes that do not affect code meaning (e.g. formatting) |
| `refactor` | Code changes that neither fix a bug nor add a feature     |
| `perf`     | A code change that improves performance                   |
| `test`     | Adding missing tests or correcting existing ones          |
| `chore`    | Changes to the build process or auxiliary tools           |
| `revert`   | Reverts a previous commit                                 |

---

### Examples of Valid Commit Messages

```bash
feat: add user login functionality
fix: correct typo in variable name
docs: update README with usage instructions
style: format code using prettier
refactor: simplify authentication logic
perf: optimize image loading time
test: add unit tests for auth service
chore: update dependencies
revert: revert "feat: add user login functionality"


---
```
