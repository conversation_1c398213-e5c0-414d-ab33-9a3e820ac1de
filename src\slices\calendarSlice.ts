/* eslint-disable no-param-reassign */
import type { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@reduxjs/toolkit';

export interface CalendarState {
  startDate: string | null;
  endDate: string | null;
  ranges: Record<string, string>; // 🔹 store API result (date → rangeType)
}

const initialState: CalendarState = {
  startDate: null,
  endDate: null,
  ranges: {}, // empty initially
};

const calendarSlice = createSlice({
  name: 'calendar',
  initialState,
  reducers: {
    setDateRange: (
      state,
      action: PayloadAction<{ startDate: string; endDate: string }>
    ) => {
      state.startDate = action.payload.startDate;
      state.endDate = action.payload.endDate;
    },
    setRanges: (state, action: PayloadAction<Record<string, string>>) => {
      state.ranges = action.payload;
    },
    resetCalendar: () => initialState,
  },
});

export const { setDateRange, setRanges, resetCalendar } = calendarSlice.actions;
export default calendarSlice.reducer;

// Selectors
export const selectStartDate = (state: any) => state.calendar.startDate;
export const selectEndDate = (state: any) => state.calendar.endDate;
export const selectDateRange = (state: any) => ({
  startDate: state.calendar.startDate,
  endDate: state.calendar.endDate,
  ranges: state.calendar.ranges,
});
