'use client';

import { <PERSON><PERSON>, <PERSON>over, <PERSON><PERSON>Trigger, PopoverContent } from '@heroui/react';
import { useSelector, useDispatch } from 'react-redux';
import type { RootState } from '@/store/authStore';
import { setRange } from '@/slices/budgetSlice';

import {
  BankNoteIcon,
  CalendarIcon,
  LocationIcon,
  UserGroupIcon,
  PaletteIcon,
} from '@/components/icons';

import TripFilter from './primaryFliterComp/tripFilter';
import AgeSelector from './primaryFliterComp/AgeSelector';
import TagSelector from './primaryFliterComp/TagSelector';
import PriceRangeSlider from './primaryFliterComp/PriceRangeSlider';
import CustomCalendar from './primaryFliterComp/CustomCalendar';

const PrimaryFilter = () => {
  const dispatch = useDispatch();

  // ✅ Read values from Redux
  const { range } = useSelector((state: RootState) => state.budget);

  return (
    <div className="grid grid-cols-5 grid-flow-row w-full bg-white rounded-xl p-2">
      {/* LOCATION */}
      <Popover placement="bottom-start">
        <PopoverTrigger>
          <Button variant="light" className="h-auto p-2 justify-start">
            <div className="text-left">
              <p className="text-lightGray text-base font-medium opacity-50 uppercase">
                LOCATION
              </p>
              <div className="flex items-center space-x-2 mt-1.5">
                <LocationIcon size={20} isAnimation={false} />
                <p className="text-default-1000 text-sm font-medium">
                  Where to go
                </p>
              </div>
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="mt-1">
          <TripFilter />
        </PopoverContent>
      </Popover>

      {/* NUMBER OF DAYS */}
      <Popover placement="bottom-start" className="z-[0]">
        <PopoverTrigger>
          <Button variant="light" className="h-auto p-2 pl-0 justify-start ">
            <div className="border-l-2 pl-4">
              <p className="text-lightGray text-base font-medium opacity-50 uppercase">
                NUMBER OF DAY
              </p>
              <div className="flex items-center space-x-2 mt-1.5">
                <CalendarIcon size={20} isAnimation={false} />
                <p className="text-default-1000 text-sm font-medium">
                  When is it
                </p>
              </div>
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="mt-1 ">
          <CustomCalendar />
        </PopoverContent>
      </Popover>

      {/* TRAVEL STYLE */}
      <Popover placement="bottom-start">
        <PopoverTrigger>
          <Button variant="light" className="h-auto p-2 pl-0 justify-start ">
            <div className="text-left border-l-2 pl-4">
              <p className="text-lightGray text-base font-medium opacity-50 uppercase">
                TRAVEL STYLE
              </p>
              <div className="flex items-center space-x-2 mt-1.5">
                <UserGroupIcon size={20} isAnimation={false} />
                <p className="text-default-1000 text-sm font-medium">
                  What's your style
                </p>
              </div>
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="mt-1">
          <TagSelector />
        </PopoverContent>
      </Popover>

      {/* TOTAL TRAVELLERS */}
      <Popover placement="bottom-start">
        <PopoverTrigger>
          <Button variant="light" className="h-auto p-2 pl-0 justify-start">
            <div className="text-left border-l-2 pl-4">
              <p className="text-lightGray text-base font-medium opacity-50 uppercase">
                TOTAL TRAVELERS
              </p>
              <div className="flex items-center space-x-2 mt-1.5">
                <PaletteIcon size={20} isAnimation={false} />
                <p className="text-default-1000 text-sm font-medium">
                  Solo or Group
                </p>
              </div>
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="mt-1">
          <AgeSelector />
        </PopoverContent>
      </Popover>

      {/* BUDGET */}
      <Popover placement="bottom-start">
        <PopoverTrigger>
          <Button variant="light" className="h-auto p-2 pl-0 justify-start">
            <div className="text-left border-l-2 pl-4">
              <p className="text-lightGray text-base font-medium opacity-50 uppercase">
                BUDGET
              </p>
              <div className="flex items-center space-x-2 mt-1.5">
                <BankNoteIcon size={20} isAnimation={false} />
                <p className="text-default-1000 text-sm font-medium">
                  Total estimate
                </p>
              </div>
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="mt-1">
          <PriceRangeSlider
            range={range}
            setRange={val => dispatch(setRange(val))}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default PrimaryFilter;
