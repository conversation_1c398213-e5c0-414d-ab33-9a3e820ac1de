'use client';

import LandingPage from '@/screen/landing';
import { useState, useEffect } from 'react';

export default function Home() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2000); // simulate skeleton for 2 seconds

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="">
      <LandingPage isLoading={isLoading} />
    </div>
  );
}
