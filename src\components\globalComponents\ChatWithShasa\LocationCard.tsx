'use client';

import React from 'react';
import { Card, CardBody, Chip } from '@heroui/react';
import { FaStar } from 'react-icons/fa6';
import Image from 'next/image';
import { MapPointIcon } from '@/components/icons';

interface LocationData {
  id: string;
  name: string;
  country: string;
  description: string;
  image: string;
  duration: string;
  bestTime: string;
  activities: string[];
  highlights: string[];
  estimatedCost: {
    currency: string;
    amount: string;
    period: string;
  };
  rating: number;
  tags: string[];
}

interface LocationCardProps {
  location: LocationData;
}
const TravelCard: React.FC<LocationCardProps> = ({ location }) => {
  return (
    <Card
      key={location.id}
      shadow="sm"
      className="w-60 rounded-2xl overflow-hidden border border-gray-200 ml-3"
    >
      <CardBody className="p-0">
        {/* Image with duration Chip */}
        <div className="relative h-40 w-full">
          <Image
            src={location.image}
            alt={location.name}
            fill
            className="object-cover rounded-2xl"
          />
          <Chip
            size="sm"
            variant="solid"
            color="default"
            className="absolute top-2 left-2 bg-white text-black font-bold text-xs rounded-sm"
          >
            {location.duration}
          </Chip>
        </div>

        {/* Content */}
        <div className="p-3">
          <div className="flex items-center justify-between">
            <h3 className="text-[15px] font-semibold text-gray-900 truncate">
              {location.name}
            </h3>
            <div className="flex items-center gap-1">
              <FaStar className="text-yellow-400 w-4 h-4" />
              <span className="text-sm font-medium text-subtitle">
                {location.rating}
              </span>
            </div>
          </div>

          <p className="text-sm text-black flex items-center gap-1 mt-1">
            <MapPointIcon className="w-4 h-4 text-[#1A1A1A]" />
            {location.country}
          </p>
        </div>
      </CardBody>
    </Card>
  );
};

export default TravelCard;
