/* eslint-disable no-param-reassign */
import type { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@reduxjs/toolkit';

export interface LocationState {
  startLocation: string;
  destination: string;
  destinations: { destination_id: number; name: string; type: string }[];
  pickedCoords: { lat: number; lng: number } | null;
}

const initialState: LocationState = {
  startLocation: 'Wales, United Kingdom',
  destination: '',
  destinations: [],
  pickedCoords: null,
};

const locationSlice = createSlice({
  name: 'location',
  initialState,
  reducers: {
    setStartLocation: (state, action: PayloadAction<string>) => {
      state.startLocation = action.payload;
    },
    setDestination: (state, action: PayloadAction<string>) => {
      state.destination = action.payload;
    },
    setDestinations: (
      state,
      action: PayloadAction<
        { destination_id: number; name: string; type: string }[]
      >
    ) => {
      state.destinations = action.payload;
    },
    removeDestination: (state, action: PayloadAction<number>) => {
      state.destinations = state.destinations.filter(
        d => d.destination_id !== action.payload
      );
    },
    setPickedCoords: (
      state,
      action: PayloadAction<{ lat: number; lng: number } | null>
    ) => {
      state.pickedCoords = action.payload;
    },
    resetLocation: () => initialState,
  },
});

export const {
  setStartLocation,
  setDestination,
  setDestinations,
  removeDestination,
  setPickedCoords,
  resetLocation,
} = locationSlice.actions;

export default locationSlice.reducer;

// Selectors
export const selectStartLocation = (state: any) => state.location.startLocation;
export const selectDestination = (state: any) => state.location.destination;
export const selectDestinations = (state: any) => state.location.destinations;
export const selectPickedCoords = (state: any) => state.location.pickedCoords;
