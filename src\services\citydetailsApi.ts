// services/cityApi.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

interface CityDetailsResponse {
  city: string;
  country_name: string;
  [key: string]: any; // in case API returns more fields
}

export const cityApi = createApi({
  reducerPath: 'cityApi',
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_GEO_API_ENDPOINT,
    prepareHeaders: headers => {
      headers.set('accept', 'application/json');
      headers.set('Content-Type', 'application/json');
      headers.set(
        'x-api-key',
        'nxvoy_country_details_dev_dRWRG7T2okxg3lSmtlfbbagU2RmUNG1W'
      );
      return headers;
    },
  }),
  endpoints: builder => ({
    getCityDetails: builder.query<CityDetailsResponse, void>({
      query: () => ({
        url: '/api/v1/city-details',
        method: 'GET',
      }),
    }),
  }),
});

export const { useGetCityDetailsQuery, useLazyGetCityDetailsQuery } = cityApi;
