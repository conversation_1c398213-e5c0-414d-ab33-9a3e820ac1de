import type { RootState } from '@/store/authStore';

export const ItineryInputs = (state: RootState) => {
  console.log(state);
  const { destination } = state.location;
  const { startDate, endDate } = state.calendar;
  //   const { adults, children, infants } = state.travellers;
  const { selectedTags } = state.travelStyle;
  //   const { budget } = state.budget;

  return {
    origin: 'LHR',

    destination,
    type: 'airport_code',
    number_of_travellers: 3,
    adults: 2,
    children: 1,
    infants: 0,
    start_date: startDate,
    end_date: endDate,
    budget: 300000,
    travel_style: selectedTags,
  };
};
