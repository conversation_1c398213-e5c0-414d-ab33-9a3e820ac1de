// Media Lightbox Components
export { default as MediaLightbox } from './MediaLightbox';
export type { MediaLightboxProps, MediaSlide } from './MediaLightbox';

export { default as MediaThumbnail } from './MediaThumbnail';
export type { MediaThumbnailProps } from './MediaThumbnail';

// Configuration utilities
export {
  DEFAULT_MEDIA_LIGHTBOX_CONFIG,
  COMPACT_MEDIA_LIGHTBOX_CONFIG,
  GALLERY_MEDIA_LIGHTBOX_CONFIG,
  mergeMediaLightboxConfig,
  useMediaLightboxConfig,
} from './MediaLightboxConfig';
export type { MediaLightboxGlobalConfig } from './MediaLightboxConfig';

// Utility functions
export {
  isVideoUrl,
  createSlide,
  createSlidesFromRecommendation,
  createSlidesFromUrls,
  createSlidesFromUrlsWithAlts,
} from './MediaLightboxUtils';

// Existing exports (if any)
export { default as AgeSelector } from '../primaryFliterComp/AgeSelector';
export { default as CustomCalendar } from '../primaryFliterComp/CustomCalendar';
export { default as PriceRangeSlider } from '../primaryFliterComp/PriceRangeSlider';
export { default as TagSelector } from '../primaryFliterComp/TagSelector';
export { default as primaryFilter } from '../primaryFilter';
export { default as tripFilter } from '../primaryFliterComp/tripFilter';
