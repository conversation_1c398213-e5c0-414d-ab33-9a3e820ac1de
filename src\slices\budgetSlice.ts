import type { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@reduxjs/toolkit';

interface BudgetState {
  range: [number, number];
}

const initialState: BudgetState = {
  range: [0, 10000],
};

const budgetSlice = createSlice({
  name: 'budget',
  initialState,
  reducers: {
    setRange: (state, action: PayloadAction<[number, number]>) => {
      state.range = action.payload;
    },
  },
});

export const { setRange } = budgetSlice.actions;
export default budgetSlice.reducer;
