'use client';

import { Slider } from '@heroui/react';

type PriceProps = {
  range: [number, number];
  setRange: (val: [number, number]) => void; // dispatch fn
};

export default function PriceRangeSlider({ range, setRange }: PriceProps) {
  const handleMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number(e.target.value);
    if (!Number.isNaN(value)) {
      const clamped = Math.min(value, range[1]);
      setRange([Math.max(0, clamped), range[1]]);
    }
  };

  const handleMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number(e.target.value);
    if (!Number.isNaN(value)) {
      const clamped = Math.max(value, range[0]);
      setRange([range[0], Math.min(10000, clamped)]);
    }
  };

  return (
    <div className="rounded-2xl bg-white p-6 w-full max-w-sm space-y-4">
      {/* Min / Max Inputs */}
      <div className="flex gap-4">
        <input
          type="number"
          value={range[0]}
          onChange={handleMinChange}
          placeholder="Min"
          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-200"
        />
        <input
          type="number"
          value={range[1]}
          onChange={handleMaxChange}
          placeholder="Max"
          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-200"
        />
      </div>

      {/* Controlled Range Slider */}
      <Slider
        className="max-w-md"
        label="Price Range"
        minValue={0}
        maxValue={10000}
        step={50}
        size="sm"
        value={range}
        onChange={val => {
          if (Array.isArray(val)) {
            setRange([val[0], val[1]]);
          }
        }}
        formatOptions={{ style: 'currency', currency: 'USD' }}
      />
    </div>
  );
}
