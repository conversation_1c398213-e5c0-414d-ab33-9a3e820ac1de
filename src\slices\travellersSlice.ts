import type { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@reduxjs/toolkit';

interface TravellersState {
  adults: number;
  children: number;
  infants: number;
  elderly: number;
  showInfant: boolean;
  showElderly: boolean;
}

const initialState: TravellersState = {
  adults: 2,
  children: 0,
  infants: 0,
  elderly: 0,
  showInfant: false,
  showElderly: false,
};

const travellersSlice = createSlice({
  name: 'travellers',
  initialState,
  reducers: {
    setAdults: (state, action: PayloadAction<number>) => {
      state.adults = action.payload;
    },
    setChildren: (state, action: PayloadAction<number>) => {
      state.children = action.payload;
    },
    setInfants: (state, action: PayloadAction<number>) => {
      state.infants = action.payload;
    },
    setElderly: (state, action: PayloadAction<number>) => {
      state.elderly = action.payload;
    },
    toggleShowInfant: state => {
      state.showInfant = !state.showInfant;
    },
    toggleShowElderly: state => {
      state.showElderly = !state.showElderly;
    },
  },
});

export const {
  setAdults,
  setChildren,
  setInfants,
  setElderly,
  toggleShowInfant,
  toggleShowElderly,
} = travellersSlice.actions;

export default travellersSlice.reducer;
