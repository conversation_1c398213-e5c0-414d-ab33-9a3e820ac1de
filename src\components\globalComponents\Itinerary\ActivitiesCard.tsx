'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { BsImageAlt } from 'react-icons/bs';
import { IoIosArrowUp } from 'react-icons/io';
import type { Flight } from '@/types/flight';
import { Divider } from '@heroui/react';

type Props = {
  flight: Flight;
};

export default function ActivitiesCard({ flight }: Props) {
  const [isOpen] = useState(true);

  return (
    <div className="w-full mx-auto overflow-hidden  rounded-lg">
      <div className="flex flex-row gap-4">
        <div className="min-w-[78px] text-center">
          <p className=" font-medium text-base mt-1">10:00</p>
        </div>

        <div className="w-full">
          <div className="flex flex-row  items-center">
            {isOpen ? (
              <>
                <div className="w-2 h-2 min-w-2 max-w-2 rounded-full bg-primary-200 mr-2 -ml-4" />
                <div className="flex h-7 items-center  text-small">
                  <Divider orientation="vertical" className="w-1 bg-black" />
                </div>
              </>
            ) : null}
            {/* Header */}
            <div
              className={`flex flex-row w-full items-center ${
                !isOpen ? 'border border-gray-300 rounded-lg' : ' '
              }`}
            >
              <div className="w-full">
                <div className="flex items-center w-full text-sm px-4 py-2">
                  <BsImageAlt className="z-10 text-black" size={20} />
                  <span className="text-subtitle mr-3 ml-3 font-bold text-sm">
                    Activities
                  </span>
                  <span className="truncate text-base text-subtitle font-medium ">
                    {flight.from} to {flight.to}
                  </span>
                  <span className="mx-2 text-gray-400">|</span>
                  <span className="text-base text-black">
                    {flight.duration}
                  </span>
                  {isOpen && (
                    <div
                      // onClick={() => setIsOpen(!isOpen)}
                      // onKeyDown={e => {
                      //   if (e.key === 'Enter' || e.key === ' ') {
                      //     setIsOpen(!isOpen);
                      //   }
                      // }}
                      role="button"
                      tabIndex={0}
                      className=" cursor-pointer"
                    >
                      <IoIosArrowUp
                        className="z-10 text-black ml-2"
                        size={20}
                      />
                    </div>
                  )}
                  {isOpen && (
                    <div className="ml-auto flex gap-4 text-xs">
                      <button
                        type="button"
                        className="text-primary-200 hover:underline"
                      >
                        Remove
                      </button>
                      <button
                        type="button"
                        className="text-primary-200 hover:underline"
                      >
                        Change
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Main Content with smooth transition */}
          <div
            className={`transition-all duration-500 ease-in-out overflow-hidden ${
              isOpen ? 'max-h-[1000px] opacity-100' : 'max-h-0 opacity-0'
            }`}
          >
            <div className="flex flex-col md:flex-row justify-between items-center px-4 py-3 text-sm">
              {/* Airline Logos */}
              <div className="flex flex-row gap-3 items-center">
                <div className="flex flex-col gap-3 justify-center md:justify-start border border-cardborder aspect-square w-[80px] h-[80px] rounded-lg">
                  {flight.airlines.map(airline => (
                    <Image
                      key={airline.name}
                      src={airline.logo}
                      alt={airline.name}
                      width={80}
                      height={80}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ))}
                </div>
                <div className="md:w-[150px]">
                  <p className="text-black font-semibold">
                    History, Museum, Explore, Art
                  </p>
                  <p className="text-default-700">Atlantic Aviasion ABQ</p>
                </div>
              </div>

              {/* Departure */}
              <div>
                <p className="line-clamp-4">
                  Explore the enchanting Neuschwanstein Castle, a fairy-tale
                  fortress nestled in the Bavarian Alps. Known for its stunning
                  architecture and breathtaking views, this iconic landmark
                  offers guided tours that delve into its rich history and the
                  inspiration behind its design. Don't miss the chance to
                  capture picturesque moments in the surrounding landscape!
                </p>
              </div>

              {/* Baggage */}
              <div className=" min-w-[120px] text-left text-default-700 pl-4 border-l">
                <p>Evening Activity</p>
                <p className="leading-3.5 mt-1">Pick up and Drop included</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
