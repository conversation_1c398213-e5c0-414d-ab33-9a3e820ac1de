'use client';

import { useDispatch, useSelector } from 'react-redux';
import { useGetTravelStyleSuggestionsMutation } from '@/services/travelStyleApi';
import { toggleTag, selectSelectedTags } from '@/slices/travelStyleSlice';
import { X, Plus } from 'lucide-react';
import { Button } from '@heroui/react';
import { useEffect } from 'react';
import { useSelector as useAppSelector } from 'react-redux';
import { useSession } from 'next-auth/react';

export default function TagSelector() {
  const dispatch = useDispatch();
  const selectedTags = useSelector(selectSelectedTags);
  const [getTravelStyles, { data, isLoading }] =
    useGetTravelStyleSuggestionsMutation();
  const { data: session, status } = useSession();

  // 🔑 pull token and guest_id from Redux

  const { token, user } = useAppSelector((state: any) => {
    // console.log("state",state);
    return state.auth;
  });

  useEffect(() => {
    // console.log("token",token)
    getTravelStyles({
      token: session?.accessToken,
      guest_id: session?.user?.guest_id,
    });
  }, [session?.accessToken, session?.user?.guest_id, getTravelStyles]);

  // ✅ only get tags from "Default" style
  const defaultStyle = data?.detail?.data.find(
    style => style.style === 'Default'
  );
  const allTags = defaultStyle?.tags ?? [];

  if (isLoading) return <div>Loading...</div>;

  return (
    <div className="rounded-2xl bg-white p-6 w-full max-w-xl space-y-4">
      <div className="flex justify-between items-center flex-wrap gap-4">
        {/* Tags */}
        <div className="flex flex-wrap gap-3 flex-1 min-w-0">
          {allTags.map(tag => {
            const isSelected = selectedTags.includes(tag);
            return (
              <div
                key={tag}
                onClick={() => dispatch(toggleTag(tag))}
                onKeyDown={e => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    dispatch(toggleTag(tag));
                  }
                }}
                role="button"
                tabIndex={0}
                className={`flex flex-row items-center gap-1 border border-primary-200 px-3 py-1.5 rounded-md text-sm cursor-pointer transition max-w-full truncate ${
                  isSelected
                    ? 'bg-primary-200/10 text-primary-200'
                    : 'text-primary-200 hover:bg-purple-50'
                }`}
              >
                <span className="truncate max-w-full -mt-1 font-semibold">
                  {tag}
                </span>
                {isSelected && (
                  <X className="h-3.5 w-3.5 text-primary-200 shrink-0" />
                )}
              </div>
            );
          })}
        </div>

        {/* Add More */}
        <Button
          variant="light"
          className="flex items-center gap-1 text-sm text-gray-500 hover:text-gray-700 whitespace-nowrap"
        >
          <Plus className="w-3.5 h-3.5" />
          add more
        </Button>
      </div>
    </div>
  );
}
