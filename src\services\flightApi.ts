import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

// Types for the API
interface FlightSearchRequest {
  request_uuid: string;
  origin: string;
  destination: string;
  departure_date: string;
  return_date?: string;
  adults: number;
  children: number;
  infants: number;
  travel_class: string;
  trip_type: string;
  direct_only: boolean;
}

interface FlightSuggestionRequest {
  query: string;
}

interface FlightProcessRequest {
  outward_id: string;
  request_uuid: string;
  routing_id: string;
  return_id?: string;
}

interface Suggestion {
  iata_code: string;
  city_name_original: string;
  airport_name: string;
  country_name: string;
}

export const flightApi = createApi({
  reducerPath: 'flightApi',
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT,
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as any).auth?.token;

      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['FlightSearch', 'FlightSuggestion'],
  endpoints: builder => ({
    // Flight search endpoint
    searchFlights: builder.mutation<any, FlightSearchRequest>({
      query: searchData => ({
        url: 'flight/search',
        method: 'POST',
        body: searchData,
      }),
      invalidatesTags: ['FlightSearch'],
    }),

    // Flight suggestions endpoint
    getFlightSuggestions: builder.mutation<
      { detail: { data: Suggestion[] } },
      FlightSuggestionRequest
    >({
      query: suggestionData => ({
        url: 'flight/suggest',
        method: 'POST',
        body: suggestionData,
      }),
      invalidatesTags: ['FlightSuggestion'],
    }),

    // Flight process endpoint
    processFlightBooking: builder.mutation<any, FlightProcessRequest>({
      query: processData => ({
        url: 'flight/flight-process',
        method: 'POST',
        body: processData,
      }),
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useSearchFlightsMutation,
  useGetFlightSuggestionsMutation,
  useProcessFlightBookingMutation,
} = flightApi;
