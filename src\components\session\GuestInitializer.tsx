'use client';

import { useEffect } from 'react';
import { useSession, signIn } from 'next-auth/react';

export default function GuestInitializer() {
  const { data: session, status } = useSession();

  useEffect(() => {
    // Only trigger guest login if:
    // 1. Session check is finished
    // 2. No session exists
    if (status === 'unauthenticated') {
      signIn('guest', { redirect: false });
    }
  }, [status]);

  return null; // doesn't render UI
}
