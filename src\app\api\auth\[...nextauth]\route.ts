import NextAuth from 'next-auth';
import Cred<PERSON><PERSON><PERSON><PERSON>ider from 'next-auth/providers/credentials';
import type { JWT } from 'next-auth/jwt';

// Your API endpoint
const API_BASE = process.env.NEXT_PUBLIC_API_ENDPOINT;

const handler = NextAuth({
  providers: [
    // Normal login
    CredentialsProvider({
      id: 'credentials',
      name: 'Credentials',
      credentials: {
        email: { label: 'Email', type: 'text' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        const res = await fetch(`${API_BASE}/api/v1/auth/signin`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(credentials),
        });

        if (!res.ok) return null;
        const data = await res.json();
        console.log('expires', data);
        if (data?.detail?.data) {
          return {
            id: data.detail.data.user.id,
            email: data.detail.data.user.email,
            firstname: data.detail.data.user.firstName,
            lastname: data.detail.data.user.lastName,
            accessToken: data.detail.data.accessToken,
            refreshToken: data.detail.data.refreshToken,
            // expiresIn: data.detail.data.accessTokenExpireOn,
            expiresIn: data.detail.data.accessTokenExpireOn,
          };
        }

        return null;
      },
    }),

    CredentialsProvider({
      id: 'guest',
      name: 'Guest',
      credentials: {},
      async authorize() {
        try {
          const res = await fetch(`${API_BASE}/api/v1/auth/guest-login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
          });

          if (!res.ok) {
            console.error('Guest login API failed', res.statusText);
            return null;
          }

          const data = await res.json();
          console.log('guest login response', data);

          if (data?.detail?.data?.guest_id) {
            return {
              id: data.detail.data.guest_id, // required by NextAuth
              guest_id: data.detail.data.guest_id,
              firstname: 'Guest',
              guest: true,
            };
          }

          return null;
        } catch (error) {
          console.error('Guest login authorize error:', error);
          return null;
        }
      },
    }),
  ],

  session: { strategy: 'jwt' },

  callbacks: {
    async jwt({ token, user }: { token: JWT; user?: any }) {
      let newToken = { ...token };

      if (user) {
        newToken = {
          ...newToken,
          accessToken: user.accessToken,
          refreshToken: user.refreshToken,
          expiresIn: user.expiresIn ? Date.now() + user.expiresIn * 1000 : null,
          user: {
            id: user.id,
            email: user.email,
            firstname: user.firstname,
            lastname: user.lastname,
            guest: user.guest,
            guest_id: user.guest_id ?? null,
          },
        };
      }

      // 🔄 Refresh if expired
      if (newToken.expiresIn && Date.now() > newToken.expiresIn) {
        try {
          const res = await fetch(`${API_BASE}/api/v1/auth/refresh`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ refreshToken: newToken.refreshToken }),
          });
          const data = await res.json();

          if (data?.detail?.data?.accessToken) {
            newToken = {
              ...newToken,
              accessToken: data.detail.data.accessToken,
              refreshToken:
                data.detail.data.refreshToken ?? newToken.refreshToken,
              expiresIn:
                Date.now() + data.detail.data.accessTokenExpireOn * 1000,
            };
          }
        } catch {
          newToken = {} as JWT;
        }
      }

      return newToken;
    },

    async session({ session, token }) {
      return {
        ...session,
        user: token.user,
        accessToken: token.accessToken,
        refreshToken: token.refreshToken,
        guest_id: token.user?.guest_id ?? null,
      };
    },
  },

  pages: {
    signIn: '/login', // optional custom login page
  },
});

export { handler as GET, handler as POST };
