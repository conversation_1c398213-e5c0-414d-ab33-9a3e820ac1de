'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { FaPlane } from 'react-icons/fa';
import { IoIosArrowUp } from 'react-icons/io';
import type { Flight } from '@/types/flight';
import { Button, Divider } from '@heroui/react';

type Props = {
  flight: Flight;
  onChangeClick?: () => void;
};

export default function FlightCard({ flight, onChangeClick }: Props) {
  const [isOpen, setIsOpen] = useState(true);

  return (
    <div className="w-full mx-auto overflow-hidden  rounded-lg">
      <div className="flex flex-row gap-4">
        <div className="min-w-[78px] text-center">
          {!isOpen ? (
            <Button
              color="primary"
              variant="flat"
              size="md"
              onPress={() => setIsOpen(!isOpen)}
              className="text-lg text-subtitle"
            >
              +
            </Button>
          ) : (
            <p className=" font-medium text-base mt-1">10:00</p>
          )}
        </div>

        <div className="w-full">
          <div className="flex flex-row  items-center">
            {isOpen ? (
              <>
                <div className="w-2 h-2 min-w-2 max-w-2 rounded-full bg-primary-200 mr-2 -ml-4" />
                <div className="flex h-7 items-center  text-small">
                  <Divider orientation="vertical" className="w-1 bg-black" />
                </div>
              </>
            ) : null}

            {/* Header */}
            <div
              className={`flex flex-row w-full items-center ${
                !isOpen ? 'border border-gray-300 rounded-lg' : ''
              }`}
            >
              <div className="w-full">
                <div className="flex items-center w-full text-sm px-4 py-2">
                  <FaPlane className="z-10 text-black -rotate-90" size={20} />
                  <span className="text-subtitle mr-3 ml-3 font-bold text-sm">
                    Flight
                  </span>
                  <span className="truncate text-base text-subtitle font-medium ">
                    {flight.from} to {flight.to}
                  </span>
                  <span className="mx-2 text-gray-400">|</span>
                  <span className="text-base text-black">
                    {flight.duration}
                  </span>
                  {isOpen && (
                    <div
                      onClick={() => setIsOpen(!isOpen)}
                      onKeyDown={e => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          setIsOpen(!isOpen);
                        }
                      }}
                      role="button"
                      tabIndex={0}
                      className="cursor-pointer"
                    >
                      <IoIosArrowUp
                        className="z-10 text-black ml-2"
                        size={20}
                      />
                    </div>
                  )}
                  {isOpen && (
                    <div className="ml-auto flex gap-4 text-xs">
                      <button
                        type="button"
                        className="text-primary-200 hover:underline"
                      >
                        Remove
                      </button>
                      <button
                        type="button"
                        className="text-primary-200 hover:underline"
                        onClick={onChangeClick}
                      >
                        Change
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          {/* Main Content with smooth transition */}
          <div
            className={`transition-all duration-500 ease-in-out overflow-hidden ${
              isOpen ? 'max-h-[1000px] opacity-100' : 'max-h-0 opacity-0'
            }`}
          >
            <div className="flex flex-col md:flex-row justify-between items-center px-4 py-3 text-sm">
              {/* Airline Logos */}
              <div className="flex flex-row gap-3 items-center">
                <div className="flex flex-col gap-3 justify-center md:justify-start border border-cardborder aspect-square w-[80px] h-[80px] rounded-lg p-3">
                  {flight.airlines.map(airline => (
                    <Image
                      key={airline.name}
                      src={airline.logo}
                      alt={airline.name}
                      width={24}
                      height={24}
                      className="h-[24px] object-contain"
                    />
                  ))}
                </div>
                <div>
                  <p className="text-default-700">{flight.departure.date}</p>
                  <p className="text-default-700">
                    {flight.departure.location}
                  </p>
                </div>
              </div>

              {/* Departure */}
              <div>
                <p className="text-lg font-semibold text-subtitle">
                  {flight.departure.time}
                </p>
                <p className="text-default-700">{flight.departure.date}</p>
                <p className="text-default-700">{flight.departure.location}</p>
              </div>

              {/* Flight Path */}
              <div className="w-[120px] flex flex-col items-center text-default-700">
                <div className="relative w-full h-6 flex items-center justify-center">
                  <div className="w-full border-t border-default-400 rotate-90 md:rotate-0" />
                  <FaPlane
                    className="z-10 text-default-400 rotate-90 md:rotate-0 absolute left-1/2 -translate-x-1/2"
                    size={24}
                  />
                </div>
                <p className="mt-1 text-md font-medium text-subtitle">
                  {flight.stops}
                </p>
                <p className="text-md font-medium text-subtitle">
                  {flight.stopDetails}
                </p>
              </div>

              {/* Arrival */}
              <div>
                <p className="text-lg font-semibold text-subtitle">
                  {flight.arrival.time}
                </p>
                <p className="text-default-700">{flight.arrival.date}</p>
                <p className="text-default-700">{flight.arrival.location}</p>
              </div>

              {/* Baggage */}
              <div className="text-left text-default-700 pl-4 border-l">
                <p>Cabin : {flight.baggage.cabin}</p>
                <p>Check-in: {flight.baggage.checkin}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
