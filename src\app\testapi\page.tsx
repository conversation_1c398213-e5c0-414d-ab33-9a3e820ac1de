'use client';

import { useGetAllProductsQuery } from '@/services/dummyApi';
import {
  selectPassengerCounts,
  updatePassengers,
} from '@/slices/flightSearchSlice';
import type { RootState } from '@/store/authStore';
import { useDispatch, useSelector } from 'react-redux';

export default function TestAPI() {
  const dispatch = useDispatch();
  // const searchData = useSelector((state: RootState) => state.flightSearchForm);

  dispatch(updatePassengers({ adults: 2, childrenCount: 4, infants: 1 }));

  // console.log("searchData", searchData)

  const { data, isLoading } = useGetAllProductsQuery();

  // console.log(data)

  if (isLoading) {
    return <>API making call. Loading...</>;
  }

  return data?.products.map((item: any) => (
    <p className="text-green-400 px-4">{item.title}</p>
  ));
}
