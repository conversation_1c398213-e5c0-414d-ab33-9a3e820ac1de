// src/services/travelStyleApi.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

interface TravelStyle {
  style: string;
  tags: string[];
}

interface TravelStyleResponse {
  message: string;
  detail: {
    data: TravelStyle[];
  };
}

export const travelStyleApi = createApi({
  reducerPath: 'travelStyleApi',
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT,
  }),
  tagTypes: ['TravelStyle'],
  endpoints: builder => ({
    getTravelStyleSuggestions: builder.mutation<
      TravelStyleResponse,
      // eslint-disable-next-line camelcase

      { token?: string; guest_id?: string }
    >({
      query: ({ token, guest_id }) => {
        const headers: Record<string, string> = {};
        if (token) headers.authorization = `Bearer ${token}`;
        // eslint-disable-next-line camelcase
        if (guest_id) headers['x-guest-id'] = guest_id;

        return {
          url: '/api/v1/activities/suggestions/travel-style',
          method: 'GET',
          headers,
        };
      },
      invalidatesTags: ['TravelStyle'],
    }),
  }),
});

export const { useGetTravelStyleSuggestionsMutation } = travelStyleApi;
