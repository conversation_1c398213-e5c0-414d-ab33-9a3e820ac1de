'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, ModalBody, useDisclosure } from '@heroui/react';
import type { ChangeEvent, FormEvent } from 'react';
import { useEffect, useRef, useState } from 'react';
import { IoClose } from 'react-icons/io5';
import { FaReg<PERSON>yeSlash, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaReg<PERSON>ye } from 'react-icons/fa6';
import * as Yup from 'yup';
import SocialLogin from './SocialLogin';
import { useSignupMutation } from '@/services/authApi';
import { v4 as uuidv4 } from 'uuid';

interface FormValues {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirm_password: string;
  sessionid?: string;
}

interface SignUpRequest {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirm_password: string;
  sessionid: string;
}

const validationSchema: Yup.ObjectSchema<FormValues> = Yup.object({
  firstName: Yup.string()
    .min(2, 'Too short!')
    .required('First name is required'),
  lastName: Yup.string().min(2, 'Too short!').required('Last name is required'),
  email: Yup.string().required('Email is required').email('Invalid email'),
  sessionid: Yup.string().optional(),
  password: Yup.string()
    .required('Password is required')
    .matches(/[a-z]/, 'At least one lowercase letter required')
    .matches(/[A-Z]/, 'At least one uppercase letter required')
    .matches(/\d/, 'At least one number required')
    .matches(/[^a-zA-Z0-9]/, 'At least one special character required')
    .min(8, 'Password must be at least 8 characters'),
  confirm_password: Yup.string()
    .oneOf([Yup.ref('password')], 'Passwords must match')
    .required('Confirm Password is required'),
});

function SignupModal({
  open,
  setOpen,
  switchToLogin,
  isOpenOTP,
  onSendEmail,
}: {
  open: boolean;
  setOpen: (value: boolean) => void;
  switchToLogin: (value: boolean) => void;
  isOpenOTP: (val: boolean) => void;
  onSendEmail: (value: string) => void;
}) {
  const { isOpen, onOpen, onClose, onOpenChange } = useDisclosure();

  useEffect(() => {
    if (open) onOpen();
    else onClose();
  }, [open, onOpen, onClose]);

  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState<FormValues>({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirm_password: '',
    sessionid: uuidv4(),
  });
  const [errors, setErrors] = useState<Partial<FormValues>>({});
  const [showValidation, setShowValidation] = useState(false);
  const [signup] = useSignupMutation();
  const [showSuccessMessage, setShowSuccessMessage] = useState('');
  const inputRef = useRef<HTMLInputElement | null>(null);
  const boxRef = useRef<HTMLDivElement | null>(null);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    setErrors({});
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    if (name === 'password') setShowValidation(value.length > 0);
  };

  const hasLowerCase = /[a-z]/.test(formData.password);
  const hasUpperCase = /[A-Z]/.test(formData.password);
  const hasNumber = /\d/.test(formData.password);
  const hasSpecialChar = /[^a-zA-Z0-9]/.test(formData.password);
  const hasMinLength = formData.password.length >= 8;

  useEffect(() => {
    function handleClickOutside(e: MouseEvent) {
      if (
        boxRef.current &&
        !boxRef.current.contains(e.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(e.target as Node)
      ) {
        setShowValidation(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    try {
      await validationSchema.validate(formData, { abortEarly: false });
      setErrors({});
      onSendEmail(formData.email);

      const signupRes = await signup(formData as SignUpRequest).unwrap();
      if (signupRes.detail.status === 'success') {
        setOpen(false);
        isOpenOTP(true);
      }
      setShowSuccessMessage(signupRes.detail.message);
    } catch (err: any) {
      if (err instanceof Yup.ValidationError) {
        const formErrors: Partial<FormValues> = {};
        err.inner.forEach((error: any) => {
          if (error.path)
            formErrors[error.path as keyof FormValues] = error.message;
        });
        setErrors(formErrors);
      } else if (err?.data?.detail?.message) {
        setShowSuccessMessage(err.data.detail.message);
      } else {
        setShowSuccessMessage(err);
      }
    }
  };

  const handleClose = () => {
    setOpen(false);
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      onClose={handleClose}
      size="5xl"
      scrollBehavior="outside"
    >
      <ModalContent>
        <ModalBody>
          <div className="grid grid-cols-2 max-md:grid-cols-1">
            <div className="w-full p-10 space-y-5 max-md:p-4">
              <h2 className="text-3xl font-bold text-gradient1">
                Your Journey Begins Here!
              </h2>
              <p className="text-[#080236]">
                Sign up to explore, plan, and create the perfect itinerary for
                your trips.
              </p>
              <form className="space-y-4" onSubmit={handleSubmit}>
                <div className="flex flex-col gap-4 sm:flex-row">
                  <div className="sm:w-1/2 w-full">
                    <label className="block font-semibold mb-1 text-[#1E1E76]">
                      First Name
                    </label>
                    <input
                      type="text"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleChange}
                      placeholder="Enter your First Name"
                      className="w-full px-4 py-2 rounded-full border border-gray-300 focus:ring-2 focus:ring-purple-300 outline-none"
                    />
                    {errors.firstName && (
                      <p className="text-red-500 text-sm mt-1">
                        {errors.firstName}
                      </p>
                    )}
                  </div>
                  <div className="w-full sm:w-1/2">
                    <label className="block font-semibold mb-1 text-[#1E1E76]">
                      Last Name
                    </label>
                    <input
                      type="text"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleChange}
                      placeholder="Enter your Last Name"
                      className="w-full px-4 py-2 rounded-full border border-gray-300 focus:ring-2 focus:ring-purple-300 outline-none"
                    />
                    {errors.lastName && (
                      <p className="text-red-500 text-sm mt-1">
                        {errors.lastName}
                      </p>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block font-semibold mb-1 text-[#1E1E76]">
                    Email
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    placeholder="Enter your Email"
                    className="w-full px-4 py-2 rounded-full border border-gray-300 focus:ring-2 focus:ring-purple-300 outline-none"
                  />
                  {errors.email && (
                    <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                  )}
                </div>

                {/* <div>
                  <label className="block font-semibold mb-1 text-[#1E1E76]">
                    Phone
                  </label>
                  <input
                    type="text"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    placeholder="Enter your Phone number"
                    className="w-full px-4 py-2 rounded-full border border-gray-300 focus:ring-2 focus:ring-purple-300 outline-none"
                  />
                  {errors.phone && (
                    <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
                  )}
                </div> */}

                <div className="relative">
                  <label className="block font-semibold mb-1 text-[#1E1E76]">
                    Create a Password
                  </label>
                  <div className="relative">
                    <input
                      type={showPassword ? 'text' : 'password'}
                      name="password"
                      value={formData.password}
                      onChange={handleChange}
                      ref={inputRef}
                      placeholder="Enter your password"
                      onFocus={() => {
                        if (formData.password.length > 0)
                          setShowValidation(true);
                      }}
                      className="w-full px-4 py-2 rounded-full border border-gray-300 focus:ring-2 focus:ring-purple-300 outline-none"
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-3 flex items-center text-gray-400"
                      aria-label={
                        showPassword ? 'Hide password' : 'Show password'
                      }
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <FaRegEyeSlash /> : <FaRegEye />}
                    </button>
                  </div>

                  {showValidation && (
                    <div
                      ref={boxRef}
                      className="absolute left-0 mt-2 w-full bg-white border rounded-lg shadow p-3 text-sm z-10"
                    >
                      <p className="text-sm font-medium mb-2">
                        Password Requirements:
                      </p>
                      <ul className="space-y-1 text-sm">
                        <li
                          className={`flex gap-1 items-center ${hasLowerCase ? 'text-green-600' : 'text-black'}`}
                        >
                          {hasLowerCase ? <FaCheck /> : <IoClose />} At least
                          one lowercase letter
                        </li>
                        <li
                          className={`flex gap-1 items-center ${hasUpperCase ? 'text-green-600' : 'text-black'}`}
                        >
                          {hasUpperCase ? <FaCheck /> : <IoClose />} At least
                          one uppercase letter
                        </li>
                        <li
                          className={`flex gap-1 items-center ${hasNumber ? 'text-green-600' : 'text-black'}`}
                        >
                          {hasNumber ? <FaCheck /> : <IoClose />} At least one
                          number
                        </li>
                        <li
                          className={`flex gap-1 items-center ${hasSpecialChar ? 'text-green-600' : 'text-black'}`}
                        >
                          {hasSpecialChar ? <FaCheck /> : <IoClose />} At least
                          one special character
                        </li>
                        <li
                          className={`flex gap-1 items-center ${hasMinLength ? 'text-green-600' : 'text-black'}`}
                        >
                          {hasMinLength ? <FaCheck /> : <IoClose />} At least 8
                          characters
                        </li>
                      </ul>
                    </div>
                  )}
                  {errors.password && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.password}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block font-semibold mb-1 text-[#1E1E76]">
                    Confirm Password
                  </label>
                  <input
                    type="password"
                    name="confirm_password"
                    value={formData.confirm_password}
                    onChange={handleChange}
                    placeholder="Enter Confirm Password"
                    className="w-full px-4 py-2 rounded-full border border-gray-300 focus:ring-2 focus:ring-purple-300 outline-none"
                  />
                  {errors.confirm_password && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.confirm_password}
                    </p>
                  )}
                </div>

                {showSuccessMessage && (
                  <p className="text-sm mt-2 mb-4 text-center text-red-600">
                    {showSuccessMessage}
                  </p>
                )}

                <button
                  type="submit"
                  className="w-full bg-gradient-to-r from-purple-500 to-blue-400 text-white py-2 rounded-full font-semibold hover:opacity-90 transition-all"
                >
                  Send Verification Code
                </button>
              </form>

              <p className="text-sm md:text-base text-[#999] mt-4">
                Already have an account?{' '}
                <button
                  type="button"
                  className="text-brand-black italic bg-transparent p-0 underline"
                  onClick={() => switchToLogin(true)}
                >
                  Sign In!
                </button>
              </p>

              <p className="text-xs md:text-sm text-[#999] mt-2">
                By continuing you agree to NxVoy's{' '}
                <a
                  href="/terms-of-service"
                  className="underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Terms of Service
                </a>{' '}
                and acknowledge you've read our{' '}
                <a
                  href="/privacy-policy"
                  className="underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Privacy Policy
                </a>
                .
              </p>
            </div>

            <div className="w-full bg-gray-50 flex flex-col items-center justify-center gap-4 p-10 max-md:p-4 md:border-l max-md:hidden">
              <SocialLogin
                authType="signup"
                onLoginSuccess={() => {}}
                closeModal={() => onClose()}
              />
            </div>
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
}

export default SignupModal;
