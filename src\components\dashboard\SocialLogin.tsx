import React, { useEffect, useRef, useState } from 'react';
import { signIn } from 'next-auth/react';
import tracker from '@/uitls/posthogTracker';
import axios from 'axios';
import Image from 'next/image';
import { useDispatch } from 'react-redux';
import { setCredentials } from '@/slices/authSlice';

declare global {
  interface Window {
    AppleID: any;
    google: any;
  }
}
const styles = {
  button: {
    color: '#080236',
    borderRadius: '100px',
    fontWeight: 600,
    boxShadow: 'inset 0 100vw white',
    border: '1px solid #EBEBEB',
  },
};
interface SocialLoginProps {
  authType: 'signIn' | 'signup';
  onLoginSuccess?: (loggedInData: any) => void;
  closeModal: () => void;
}

const SocialLogin: React.FC<SocialLoginProps> = ({
  authType,
  onLoginSuccess,
  closeModal,
}) => {
  const [googleAuthError, setGoogleAuthError] = useState<string | null>(null);
  const [appleAuthError, setAppleAuthError] = useState<string | null>(null);
  const codeClient = useRef<any>(null);
  const dispatch = useDispatch();

  const storeUserInSession = (user: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    picture: string;
  }) => {
    if (user?.id && user?.firstName) {
      sessionStorage.setItem(
        'user',
        JSON.stringify({
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          avatar: user.picture,
        })
      );
    }
    window.dispatchEvent(new Event('storage'));
  };

  const getTrackerName = (
    provider: string,
    status: 'Started' | 'Success' | 'Failed'
  ) => {
    const action = authType === 'signup' ? 'Register' : 'Login';
    return `R1_${provider}_${action}_${status}`;
  };

  const trackLoginSuccess = (provider: string, user: any, message: string) => {
    storeUserInSession(user);
    tracker.trackEvent(getTrackerName(provider, 'Success'), {
      id: user.id,
      firstName: user.firstName,
      message,
    });
  };

  const trackLoginFailure = (
    provider: string,
    reason: string,
    message: string,
    user?: any
  ) => {
    tracker.trackEvent(getTrackerName(provider, 'Failed'), {
      reason,
      message,
      id: user?.id,
      firstName: user?.firstName,
    });
  };

  // Tracker name generator
  //   const getTrackerName = (
  //     provider: string,
  //     status: 'Started' | 'Success' | 'Failed'
  //   ) => {
  //     const action = authType === 'signup' ? 'Register' : 'Login';
  //     return `R1_${provider}_${action}_${status}`;
  //   };

  // Init Apple
  useEffect(() => {
    if (typeof window !== 'undefined' && window.AppleID) {
      window.AppleID.auth.init({
        clientId: process.env.NEXT_PUBLIC_APPLE_CLIENT_ID,
        scope: 'name email',
        redirectURI: process.env.NEXT_PUBLIC_APPLE_REDIRECT_URI,
        usePopup: true,
      });
    }
  }, []);

  // Init Google
  useEffect(() => {
    if (typeof window !== 'undefined' && window.google) {
      codeClient.current = window.google.accounts.oauth2.initCodeClient({
        client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
        scope: 'email profile openid',
        redirect_uri: process.env.NEXT_PUBLIC_GOOGLE_REDIRECT_URI,
        callback: async (response: any) => {
          if (response?.code) {
            try {
              const res = await axios.post(
                `${
                  process.env.NEXT_PUBLIC_API_ENDPOINT
                }/api/v1/auth/google/token`,
                {
                  code: response.code,
                }
              );

              if (!res.data?.detail.data?.accessToken) {
                const errorMessage =
                  res.data?.detail.message || 'Oops, Google SignIn failed!!';
                trackLoginFailure(
                  'Google',
                  errorMessage,
                  'Google login failed'
                );
                setGoogleAuthError(errorMessage);
                return;
              }
              closeModal();
              const {
                accessToken,
                refreshToken,
                accessTokenExpireOn,
                refreshTokenExpireOn,
                user,
              } = res.data.detail.data;
              localStorage.setItem('token', accessToken);
              localStorage.setItem('user', JSON.stringify(user));
              dispatch(
                setCredentials({
                  redirect: false,
                  id: user.id,
                  email: user.email,
                  ...user,
                  accessToken,
                  refreshToken,
                  accessTokenExpireOn,
                  refreshTokenExpireOn,
                })
              );
              await signIn('credentials', {
                redirect: false,
                id: user.id,
                email: user.email,
                ...user,
                accessToken,
                refreshToken,
                accessTokenExpireOn,
                refreshTokenExpireOn,
              });

              trackLoginSuccess(
                'Google',
                user,
                'User logged in with Google successfully'
              );

              onLoginSuccess?.(res.data?.detail.data);
            } catch (err) {
              trackLoginFailure(
                'Google',
                err instanceof Error ? err.message : 'API call error',
                'Google login API error'
              );
              setGoogleAuthError('Login failed. Please try again.');
            }
          } else {
            trackLoginFailure(
              'Google',
              'No authorization code received',
              'Google auth code missing'
            );
            setGoogleAuthError('No authorization code received.');
          }
        },
      });
    }
  }, [authType]);

  // Apple login handler
  const handleAppleLogin = async () => {
    tracker.trackEvent(getTrackerName('Apple', 'Started'), {
      provider: 'Apple',
    });
    if (typeof window === 'undefined' || !window.AppleID) return;

    try {
      const response = await window.AppleID.auth.signIn();
      const payload = { code: response.authorization.id_token };

      const res = await axios.post(
        `${process.env.NEXT_PUBLIC_API_ENDPOINT}auth/apple`,
        payload
      );

      if (!res.data?.detail.data?.accessToken) {
        const errorMessage =
          res.data?.detail.message || 'Oops, Apple SignIn failed!!';
        trackLoginFailure('Apple', errorMessage, 'Apple login failed');
        setAppleAuthError(errorMessage);
        return;
      }
      closeModal();
      const {
        accessToken,
        refreshToken,
        accessTokenExpireOn,
        refreshTokenExpireOn,
        user,
      } = res.data.detail.data;
      dispatch(
        setCredentials({
          redirect: false,
          id: user.id,
          email: user.email,
          ...user,
          accessToken,
          refreshToken,
          accessTokenExpireOn,
          refreshTokenExpireOn,
        })
      );
      localStorage.setItem('token', accessToken);
      localStorage.setItem('user', JSON.stringify(user));
      await signIn('credentials', {
        redirect: false,
        id: user.id,
        email: user.email,
        ...user,
        accessToken,
        refreshToken,
        accessTokenExpireOn,
        refreshTokenExpireOn,
      });

      trackLoginSuccess(
        'Apple',
        user,
        'User logged in with Apple successfully'
      );

      onLoginSuccess?.(res.data?.detail.data);
    } catch (error: any) {
      trackLoginFailure(
        'Apple',
        error instanceof Error
          ? error.message
          : 'Apple signIn rejected or failed',
        'Apple login error'
      );
      setAppleAuthError('Apple Sign In failed. Please try again.');
    }
  };

  // Google login starter
  const handleGoogleLogin = () => {
    tracker.trackEvent(getTrackerName('Google', 'Started'), {
      provider: 'Google',
    });

    if (codeClient.current) {
      codeClient.current.requestCode();
    } else {
      setGoogleAuthError('Google login client is not ready.');
    }
  };

  return (
    <div className="flex md:flex-row lg:flex-col lg:w-full font-proxima-nova gap-4 xs:gap-2 sm:flex-row xs:flex-col lg:justify-center xs:justify-center sm:items-center xs:items-center">
      {/* Google Button */}
      <button
        type="button"
        onClick={handleGoogleLogin}
        className="lg:w-full md:w-max flex sm:text-sm xs:text-xs items-center justify-center py-3 sm:py-2 xs:py-2 md:px-4 sm:px-3 xs:px-3 border border-brand-border transition"
        style={styles.button}
      >
        <Image
          src="https://storage.googleapis.com/nxvoytrips-img/social/google-icon.svg"
          alt="Google logo"
          width={20}
          height={20}
          className="mr-2 xs:mr-1"
        />
        {authType === 'signIn' ? 'Sign In With Google' : 'Sign Up With Google'}
      </button>
      {googleAuthError ? (
        <p className="text-red-600 text-sm mt-2 mb-4 text-center">
          {googleAuthError}
        </p>
      ) : null}

      {/* Apple Button */}
      <button
        type="button"
        onClick={handleAppleLogin}
        className="lg:w-full md:w-max flex sm:text-sm xs:text-xs items-center justify-center py-3 sm:py-2 xs:py-2 md:px-4 sm:px-3 xs:px-3 border border-brand-border transition"
        style={styles.button}
      >
        <Image
          src="https://storage.googleapis.com/nxvoytrips-img/social/apple-icon.svg"
          alt="Apple logo"
          width={20}
          height={20}
          className="mr-2 xs:mr-1"
        />
        {authType === 'signIn' ? 'Sign In With Apple' : 'Sign Up With Apple'}
      </button>
      {appleAuthError ? (
        <p className="text-red-600 text-sm mt-2 mb-4 text-center">
          {appleAuthError}
        </p>
      ) : null}
    </div>
  );
};
export default SocialLogin;
