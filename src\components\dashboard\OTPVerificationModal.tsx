import React, { useState, useRef, useEffect } from 'react';
import OtpInput from './OTPInput';
import {
  Modal,
  ModalContent,
  ModalBody,
  Button,
  useDisclosure,
} from '@heroui/react';
import { useOtpVerifyMutation } from '@/services/authApi';
import toast from 'react-hot-toast';

interface OtpVerificationScreenProps {
  open: boolean;
  setOpen: (value: boolean) => void;
  type?: 'email' | 'phone';
  email: string;
}

const OtpVerificationScreen: React.FC<OtpVerificationScreenProps> = ({
  open,
  setOpen,
  type = 'email',
  email,
}) => {
  const [otp, setOtp] = useState<string[]>(Array(6).fill(''));
  const [otpError, setOtpError] = useState<string | null>(null);
  // Create an array of refs, one for each input
  const inputRefs = useRef<(HTMLInputElement | null)[]>(Array(6).fill(null));
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [otpVerify] = useOtpVerifyMutation();

  useEffect(() => {
    if (open) {
      onOpen();
    } else {
      onClose();
    }
  }, [open, onOpen, onClose]);

  // Handle OTP input change
  const handleChange = (index: number, value: string) => {
    // Only allow digits
    if (!/^\d*$/.test(value)) return;

    const newOtp = [...otp];
    // Take only the last character if multiple are pasted
    newOtp[index] = value.slice(-1);
    setOtp(newOtp);
    setOtpError(null);
    // Auto-focus next input if a digit was entered
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  // Handle key down for backspace navigation
  const handleKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      // Focus previous input if current is empty and backspace is pressed
      inputRefs.current[index - 1]?.focus();
    }
  };

  // Handle paste for the entire OTP
  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text/plain').trim();

    // Check if pasted content is a number with expected length
    if (!/^\d+$/.test(pastedData)) return;

    const digits = pastedData.slice(0, 6).split('');
    const newOtp = [...otp];

    // Fill in as many inputs as we have digits
    digits.forEach((digit, index) => {
      if (index < 6) {
        newOtp[index] = digit;
      }
    });

    setOtp(newOtp);

    // Focus the next empty input or the last one
    const nextEmptyIndex = newOtp.findIndex(val => !val);
    if (nextEmptyIndex !== -1) {
      inputRefs.current[nextEmptyIndex]?.focus();
    } else {
      inputRefs.current[5]?.focus();
    }
  };

  const verifyOTP = async () => {
    try {
      const otpString: string = otp.join('');
      const credentials: any = { email, otp: otpString };
      const userData = await otpVerify(credentials).unwrap();
      toast.success('Email verified! You can now log in.');
      console.log('otp verify:', userData);
      onClose();
      setOpen(false);
      // dispatch(setCredentials(mappedUserData));
    } catch (err) {
      console.error('Login failed:', err);
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate that all OTP fields are filled
    if (otp.every(digit => digit)) {
      setOtpError(null);
      // onVerify(otp);
      // verifyOTP();
      verifyOTP();
    } else {
      setOtpError('Please enter the full verification code to continue.');
    }
  };

  // custom close handler
  const handleClose = () => {
    setOpen(false);
    onClose();
  };

  // Handle OTP completion callback
  const handleOtpComplete = (otpValue: string) => {
    // Optional: auto-submit when all digits are filled
    // onVerify();
    return otpValue;
  };
  const styles = {
    header: {
      fontFamily: 'Proxima Nova, sans-serif',
      fontWeight: 700,
      letterSpacing: '0%',
      WebkitBackgroundClip: 'text',
      backgroundClip: 'text',
    },
  };

  return (
    <Modal
      isOpen={isOpen}
      // onOpenChange={onOpenChange}
      size="5xl"
      scrollBehavior="outside"
      placement="auto"
      shouldBlockScroll={false}
      onClose={handleClose}
    >
      <ModalContent>
        <ModalBody>
          {/* <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center bg-gray-50"> */}
          <div className="p-8 flex flex-col items-center text-center max-w-2xl mx-auto bg-white rounded-lg relative">
            <h1
              className="md:text-4xl sm:text-3xl xs:text-2xl font-bold mb-4 text-brand-black"
              style={styles.header}
            >
              OTP Verification
            </h1>

            <div className="w-full mb-8">
              <p className="text-brand-black text-xl xs:text-base">
                Enter the verification code we just sent to your email/phone
                number.
              </p>
            </div>

            <h2 className="md:text-2xl sm:text-xl xs:text-lg font-bold mb-4 text-brand-black">
              Verification Code
            </h2>

            <form onSubmit={handleSubmit} className="w-full">
              <div className="mb-8">
                <OtpInput
                  otp={otp}
                  setOtp={setOtp}
                  inputCount={6}
                  onComplete={handleOtpComplete}
                  onChange={handleChange}
                  onKeyDown={handleKeyDown}
                  onPaste={handlePaste}
                  inputRefs={inputRefs}
                />
                {otpError && (
                  <p className="text-red-500 text-sm mt-2">{otpError}</p>
                )}
              </div>
              <Button
                type="submit"
                className="bg-gradient-to-r from-purple-500 to-blue-400 text-white py-2 rounded-full font-semibold hover:opacity-90 transition-all"
                onClick={verifyOTP}
              >
                Create Account
              </Button>
              {/* <div className="flex justify-center mb-6">
                  <Button type="submit" className="max-w-sm">
                    {loadingEmailOtpVerify
                      ? 'Verifying...'
                      : type === 'email'
                        ? 'Change email'
                        : 'Change phone'}
                  </Button>
                </div>
                <div className="flex justify-center mb-6">
                  <div
                    className="max-w-sm cursor-pointer"
                    onClick={() => {
                      handleResend();
                    }}
                  >
                    {loadingEmailOtp ? 'Resending...' : 'Resend code'}
                  </div>
                </div> */}
            </form>
          </div>
          {/* </div> */}
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default OtpVerificationScreen;
