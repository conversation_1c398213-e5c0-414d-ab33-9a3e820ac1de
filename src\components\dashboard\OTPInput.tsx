import React, { useRef } from 'react';

interface OtpInputProps {
  otp: string[];
  setOtp: React.Dispatch<React.SetStateAction<string[]>>;
  inputCount?: number;
  onComplete?: (otpValue: string) => void;
  // Optional overrides from parent (for validation/UI)
  onChange?: (index: number, value: string) => void;
  onKeyDown?: (index: number, e: React.KeyboardEvent<HTMLInputElement>) => void;
  onPaste?: (e: React.ClipboardEvent<HTMLInputElement>) => void;
  inputRefs?: React.MutableRefObject<(HTMLInputElement | null)[]>;
}

const OtpInput: React.FC<OtpInputProps> = ({
  otp,
  setOtp,
  inputCount = 6,
  onComplete,
  onChange,
  onKeyDown,
  onPaste,
  inputRefs,
}) => {
  const localRefs = useRef<(HTMLInputElement | null)[]>(
    Array(inputCount).fill(null)
  );
  const refs = inputRefs || localRefs;

  // Handle OTP input change
  const handleChange: any = (index: number, value: string) => {
    // Only allow digits
    if (!/^\d*$/.test(value)) return;

    const newOtp = [...otp];
    // Take only the last character if multiple are pasted
    newOtp[index] = value.slice(-1);
    setOtp(newOtp);

    // Auto-focus next input if a digit was entered
    if (value && index < inputCount - 1) {
      refs.current[index + 1]?.focus();
    }

    // Check if OTP is complete
    if (value && index === inputCount - 1) {
      const completeOtp = [...newOtp.slice(0, index), value].join('');
      if (completeOtp.length === inputCount && onComplete) {
        onComplete(completeOtp);
      }
    }
    onChange?.(index, value);
  };

  // Handle key down for backspace navigation
  const handleKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      // Focus previous input if current is empty and backspace is pressed
      refs.current[index - 1]?.focus();
    }
    onKeyDown?.(index, e);
  };

  // Handle paste for the entire OTP
  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text/plain').trim();

    // Check if pasted content is a number
    if (!/^\d+$/.test(pastedData)) return;

    const digits = pastedData.slice(0, inputCount).split('');
    const newOtp = [...otp];

    // Fill in as many inputs as we have digits
    digits.forEach((digit, index) => {
      if (index < inputCount) {
        newOtp[index] = digit;
      }
    });

    setOtp(newOtp);

    // Focus the next empty input or the last one
    const nextEmptyIndex = newOtp.findIndex(val => !val);
    if (nextEmptyIndex !== -1) {
      refs.current[nextEmptyIndex]?.focus();
    } else {
      refs.current[inputCount - 1]?.focus();

      // If OTP is complete after paste, trigger onComplete
      if (newOtp.filter(Boolean).length === inputCount && onComplete) {
        onComplete(newOtp.join(''));
      }
    }
    onPaste?.(e);
  };

  return (
    <div className="flex justify-center gap-2">
      {Array.from({ length: inputCount }).map((_, index) => (
        <div key={`otp-input-${index}`} className="p-[2px] rounded-md border">
          <input
            ref={el => {
              refs.current[index] = el;
            }}
            type="text"
            value={otp[index]}
            onChange={e => handleChange(index, e.target.value)}
            onKeyDown={e => handleKeyDown(index, e)}
            onPaste={index === 0 ? handlePaste : undefined}
            maxLength={1}
            className="w-10 h-10 md:w-14 md:h-14 text-center text-xl text-brand-black bg-brand-white rounded-md focus:outline-none"
            aria-label={`Digit ${index + 1} of verification code`}
          />
        </div>
      ))}
    </div>
  );
};

export default OtpInput;
