import type { DefaultSession } from 'next-auth';

declare module 'next-auth' {
  interface Session {
    user?: {
      id?: string;
      firstname?: string;
      lastname?: string;
      guest?: boolean;
      email?: string;
      guest_id?: string;
    } & DefaultSession['user'];
    accessToken?: string;
    refreshToken?: string;
    guest_id?: string;
  }

  interface User {
    id?: string;
    firstname?: string;
    lastname?: string;
    guest?: boolean;
    email?: string;
    accessToken?: string;
    refreshToken?: string;
    expiresIn?: number;
    guest_id?: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    accessToken?: string;
    refreshToken?: string;
    expiresIn?: number | null;
    user?: {
      id?: string;
      firstname?: string;
      lastname?: string;
      guest?: boolean;
      email?: string;
      guest_id?: string;
    };
  }
}
