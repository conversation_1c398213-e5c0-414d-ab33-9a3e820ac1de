// src/services/threadApi.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

interface ThreadGenerateRequest {
  _new: boolean;
}

interface ThreadGenerateResponse {
  detail: {
    status: string;
    message: string;
    data: {
      thread_id: string;
      expires_at: string;
      created_at: string;
      reused: boolean;
      usage: string;
    };
  };
}

export const threadApi = createApi({
  reducerPath: 'threadApi',
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT,
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as any).auth?.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['Thread'],
  endpoints: builder => ({
    generateThread: builder.mutation<
      ThreadGenerateResponse,
      ThreadGenerateRequest
    >({
      query: body => ({
        url: '/api/v1/thread/generate',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['Thread'],
    }),
  }),
});

export const { useGenerateThreadMutation } = threadApi;
