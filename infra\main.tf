locals {
  workspaces = {
    "devr2" = {
      env_name       = "dev"
      dns_name       = "devr2.nxvoytrips.ai"
      static_ip_name = "dev-fer2-ingress-ip-v2"
      replicas       = "2"
      max_replicas   = "2"
      cpu_request    = var.CPU_REQUEST
      cpu_limit      = var.CPU_LIMIT
      mem_request    = var.MEM_REQUEST
      mem_limit      = var.MEM_LIMIT
    }
    "dev-int" = {
      env_name       = "dev"
      dns_name       = "dev.nxvoytrips.ai"
      static_ip_name = "dev-fer2int-ingress-ip"
      replicas       = "2"
      max_replicas   = "2"
      cpu_request    = var.CPU_REQUEST
      cpu_limit      = var.CPU_LIMIT
      mem_request    = var.MEM_REQUEST
      mem_limit      = var.MEM_LIMIT
    }
    qa = {
      env_name       = "qa"
      dns_name       = "qa.nxvoytrips.ai"
      static_ip_name = "qa-fe-ingress-ip"
      replicas       = "1"
      max_replicas   = "1"
      cpu_request    = "0"
      cpu_limit      = "0"
      mem_request    = "0"
      mem_limit      = "0"
    }
    preprod = {
      env_name       = "preprod"
      dns_name       = "preprod.nxvoytrips.ai"
      static_ip_name = "preprod-fe-ingress-ip"
      replicas       = "2"
      max_replicas   = "6"
      cpu_request    = var.CPU_REQUEST
      cpu_limit      = var.CPU_LIMIT
      mem_request    = var.MEM_REQUEST
      mem_limit      = var.MEM_LIMIT
    }
    prod = {
      env_name       = "prod"
      dns_name       = "nxvoytrips.ai"
      static_ip_name = "prod-fe-ingress-ip"
      replicas       = "2"
      max_replicas   = "6"
      cpu_request    = var.CPU_REQUEST
      cpu_limit      = var.CPU_LIMIT
      mem_request    = var.MEM_REQUEST
      mem_limit      = var.MEM_LIMIT
    }
  }

  # Get current environment config, defaulting to dev-int for safety
  env_config = lookup(local.workspaces, terraform.workspace, local.workspaces["dev-int"])

  environment  = local.env_config.env_name
  dns_name     = local.env_config.dns_name
  replicas     = local.env_config.replicas
  max_replicas = local.env_config.max_replicas
  cpu_request  = local.env_config.cpu_request
  cpu_limit    = local.env_config.cpu_limit
  mem_request  = local.env_config.mem_request
  mem_limit    = local.env_config.mem_limit

  service_name = terraform.workspace == "devr2" ? "${var.SERVICE_NAME}r2" : var.SERVICE_NAME
  node_port    = terraform.workspace == "devr2" ? "30081" : var.NODE_PORT
}

data "terraform_remote_state" "gcp" {
  backend   = "gcs"
  workspace = terraform.workspace

  config = {
    bucket         = "nxvoy-terraform-states"
    prefix         = "nxvoy"
  }
}

## Create namespace 
resource "kubernetes_namespace" "service" {
  metadata {
    name = "${local.environment}-${local.service_name}"
  }
}

 resource "google_compute_global_address" "ingress_ip" {
  name = local.env_config.static_ip_name
}
