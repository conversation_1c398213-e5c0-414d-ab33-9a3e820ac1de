import {
  Mo<PERSON>,
  <PERSON>dalContent,
  ModalHeader,
  Modal<PERSON>ody,
  Modal<PERSON>ooter,
  Button,
  useDisclosure,
} from '@heroui/react';

export default function SubscribeModal({
  open,
  onClose,
}: {
  open: boolean;
  onClose: () => void;
}) {
  return (
    <Modal
      isDismissable={false}
      isKeyboardDismissDisabled
      isOpen={open}
      onClose={onClose}
      classNames={{
        backdrop: 'z-[999999999999999]',
        wrapper: 'z-[1000000000000000000]',
        base: 'z-[1000000000000000000000]',
      }}
    >
      <ModalContent>
        {() => (
          <>
            <ModalHeader className="flex flex-col gap-1">
              Modal Title
            </ModalHeader>
            <ModalBody>
              <p>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam
                pulvinar risus non risus hendrerit venenatis. Pellentesque sit
                amet hendrerit risus, sed porttitor quam.
              </p>
              <p>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam
                pulvinar risus non risus hendrerit venenatis. Pellentesque sit
                amet hendrerit risus, sed porttitor quam.
              </p>
            </ModalBody>
            <ModalFooter>
              <Button color="danger" variant="light" onPress={onClose}>
                Close
              </Button>
              <Button color="primary" onPress={onClose}>
                Action
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}
